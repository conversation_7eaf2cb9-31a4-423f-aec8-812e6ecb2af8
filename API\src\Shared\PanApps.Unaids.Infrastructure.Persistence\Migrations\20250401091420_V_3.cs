﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PanApps.Unaids.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "program_models",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    model_id = table.Column<int>(type: "integer", nullable: false),
                    display_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_models", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_models_model_model_id",
                        column: x => x.model_id,
                        principalSchema: "pan_gms",
                        principalTable: "models",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_models_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_program_models_model_id",
                schema: "pan_gms",
                table: "program_models",
                column: "model_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_models_program_id",
                schema: "pan_gms",
                table: "program_models",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_models_tenant_id",
                schema: "pan_gms",
                table: "program_models",
                column: "tenant_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "program_models",
                schema: "pan_gms");
        }
    }
}
