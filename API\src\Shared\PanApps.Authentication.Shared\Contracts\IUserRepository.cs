﻿
using PanApps.Core.Base;
using PanApps.Unaids.Domain.System;

namespace PanApps.Authentication.Shared
{
    public interface IUserRepository: IBaseRepository<User>
    {
        Task<bool> ExistsAsync(string email);
        Task<User> GetUserByEmail(string email);
        Task<UserPermission> AddUserPermission(UserPermission userPermission);
        Task<List<User>> GetTenantUsers(int tenantId);
        Task<User> GetUser(int userId, int tenantId);
        Task<List<CommonLookUp>> GetSearchable(int id, string fieldName);
        Task<List<CommonLookUp>> GetRole();
        Task<List<CommonLookUp>> GetBusinessUnits();
        Task<UserInvitation> AddUserInvitation(UserInvitation invitation);


    }
}
