﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class CurrencyConfiguration : IEntityTypeConfiguration<Currency>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Currency> builder)
        {
            builder.ToTable("currencies");
            builder.<PERSON><PERSON>ey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.HasIndex(e => e.Code).IsUnique();
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(10);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(50);
            builder.Property(e => e.Symbol).IsRequired(false).HasMaxLength(10);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new CurrencyConfiguration());
        }
    }
}
