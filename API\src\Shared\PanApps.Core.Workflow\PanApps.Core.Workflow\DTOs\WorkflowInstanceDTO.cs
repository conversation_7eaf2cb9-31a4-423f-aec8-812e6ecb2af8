﻿namespace PanApps.Core.Workflow
{
    public class WorkflowInstanceDTO
    {
        public Guid? WorkflowInstanceId { get; set; }
        public int WorkflowDefinitionId { get; set; }
        public Guid? CorrelationId { get; set; }
        public int? PreviousStepId { get; set; }
        public int? CurrentStepId { get; set; }
        public short? ExecutionStatus { get; set; }
        public short? IsInitialStep { get; set; }
        public short? IsCompleted { get; set; }
        public short? IsReturned { get; set; }
        public int? RevisionNo { get; set; }
        public string? CurrentStatus { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedOn { get; set; }
    }
}
