﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using PanApps.Unaids.Domain;

#nullable disable

namespace PanApps.Unaids.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_10 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "workflow_transitions",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    from_step_id = table.Column<int>(type: "integer", nullable: false),
                    to_step_id = table.Column<int>(type: "integer", nullable: false),
                    property = table.Column<TransitionProperty>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_transitions", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_workflow_transitions_tenant_id",
                schema: "pan_gms",
                table: "workflow_transitions",
                column: "tenant_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "workflow_transitions",
                schema: "pan_gms");
        }
    }
}
