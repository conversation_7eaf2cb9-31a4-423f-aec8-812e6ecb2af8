namespace PanApps.Unaids.Domain.System
{
    public class OBSSetting : BaseEntity
    {
        public int OBSTypeId { get; set; }
        public virtual LookUpInfo? OBSType { get; set; }

        public int? ParentOBSTypeId { get; set; }
        public virtual LookUpInfo? ParentOBSType { get; set; }

        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;

        public short IsAccountingUnit { get; set; }
        public short? IsExpenseTrackingUnit { get; set; }

        public int Level { get; set; }
        public string Hierarchy { get; set; } = string.Empty;
        public int? SchemaDefinitionId { get; set; }
        //public virtual SchemaDefinition? SchemaDefinition { get; set; }

        public short Active { get; set; }
    }
}
