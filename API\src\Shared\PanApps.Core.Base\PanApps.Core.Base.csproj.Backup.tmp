﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="FluentValidation" Version="11.9.2" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\PanApps.Core.Shared\PanApps.Core.Shared.csproj" />
	  <ProjectReference Include="..\PanApps.Unaids.Domain\PanApps.Unaids.Domain.csproj" />
	</ItemGroup>

</Project>
