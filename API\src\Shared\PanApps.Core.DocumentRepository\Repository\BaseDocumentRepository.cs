﻿
using AutoMapper;
using Microsoft.AspNetCore.Http;
using PanApps.Core.Base;
using PanApps.PanGMS.Domain;
using PanApps.PanGMS.Infrastructure.Persistence;

namespace PanApps.Core.DocumentRepository
{
    public class BaseDocumentRepository(IUnitOfWork<CommonDbContext> unitOfWork, IMapper mapper, ISequenceRepository sequenceRepository): IDocumentRepository
    {
        /// <summary>
        /// Download the file by Id
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public async Task<DocumentResponseDto> Download(int documentId)
        {
            var documentDetail = await unitOfWork.Repository<IDocumentStoreDetailRepository>().GetByIdAsync(documentId);

            var stream = await ReadFile(documentDetail);

            return new DocumentResponseDto() { FileContent = stream, FileName = documentDetail.FileName, ContentType = "application/octet-stream" };
        }

        /// <summary>
        /// Download the file by fileName
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public async Task<DocumentResponseDto> Download(string fileName)
        {
            var documentDetail = (await unitOfWork.Repository<IDocumentStoreDetailRepository>()
                .GetManyAsync(x => x.UploadedName == fileName, null,null, null)).FirstOrDefault();

            var stream = await ReadFile(documentDetail);

            return new DocumentResponseDto() { FileContent = stream, FileName = documentDetail.FileName, ContentType = "application/octet-stream" };
        }

        /// <summary>
        /// Upload the new document
        /// </summary>
        /// <param name="files"></param>
        /// <param name="document"></param>
        /// <returns></returns>
        public async Task<List<DocumentStoreDetailDto>> Upload(IFormFileCollection files, DocumentRequestDto document)
        {
            List<DocumentStoreDetails> docs = new();

            // add document store
            var docStore = mapper.Map<DocumentStore>(document);

            var docStoreEntity = unitOfWork.Repository<IDocumentStoreRepository>().GetTableName();

            docStore.Id = await sequenceRepository.GetSequenceNumber(docStoreEntity);
            await unitOfWork.Repository<IDocumentStoreRepository>().AddAsync(docStore);

            // add document store detail
            var docdetailEntity = unitOfWork.Repository<IDocumentStoreDetailRepository>().GetTableName();
            var nextId = await sequenceRepository.GetSequenceNumber(docdetailEntity, files.Count);
            foreach (var file in files)
            {
                var fileName = file.FileName;

                var uploadedFileName = Convert.ToString(Guid.NewGuid()) + Path.GetExtension(file.FileName);

                DocumentStoreDetails docStoreDetail = new ();

                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    docStoreDetail = await this.WriteFile(ms, document, uploadedFileName);
                }

                docStoreDetail.Id = nextId;
                docStoreDetail.DocumentStoreId = docStore.Id;
                docStoreDetail.FileName = fileName;
                docStoreDetail.Title = file.Name;
                docStoreDetail.FileSize = Convert.ToInt32(file.Length);

                docs.Add(docStoreDetail);

                nextId = nextId + 1;
            }

            await unitOfWork.Repository<IDocumentStoreDetailRepository>().AddManyAsync(docs);
            await unitOfWork.SaveChangesAsync();

           
            return mapper.Map<List<DocumentStoreDetailDto>>(docs);
        }


        #region delete document...
        /// <summary>
        /// Delete the document..
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task Delete(int id)
        {
            var docDetail = await unitOfWork.Repository<IDocumentStoreDetailRepository>().GetByIdAsync(id);

            if (docDetail != null)
            {
                await DeleteFile(docDetail);

                await unitOfWork.Repository<IDocumentStoreDetailRepository>().DeleteAsync(docDetail);
                await unitOfWork.SaveChangesAsync();

                if (! (await unitOfWork.Repository<IDocumentStoreDetailRepository>().AnyDocsAsync(docDetail.DocumentStoreId)))
                {
                    var docStore = await unitOfWork.Repository<IDocumentStoreRepository>().GetByIdAsync(docDetail.DocumentStoreId);
                    await unitOfWork.Repository<IDocumentStoreRepository>().DeleteAsync(docStore);
                    await unitOfWork.SaveChangesAsync();
                }
            }
        }
        #endregion

        public virtual async Task<MemoryStream> ReadFile(DocumentStoreDetails documentStoreDetail)
        {
            throw new NotImplementedException();
        }

        public virtual async Task<DocumentStoreDetails> WriteFile(Stream file,  DocumentRequestDto document, string fileName)
        {
            throw new NotImplementedException();
        }

        public virtual async Task DeleteFile(DocumentStoreDetails documentStoreDetail)
        {
            throw new NotImplementedException();
        }
    }
}
