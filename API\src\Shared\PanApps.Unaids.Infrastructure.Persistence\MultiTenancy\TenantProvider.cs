﻿using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public class TenantProvider : ITenantProvider
    {
        private readonly string _defaultConnectionString;
        private readonly string _defaultDbSchemaName;
        private readonly IUserContext _userContext;
        private readonly Dictionary<int, TenantConfigurationOptions> _tenantSetting;

        public int? TenantId => TenantContext.CurrentTenant?.TenantId;
        public string? TenantName => TenantContext.CurrentTenant?.Tenant;

        public TenantProvider(CommonDbContext dbContext, IUserContext userContext)
        {
            //Get default tenant
           var defaultTenant = dbContext.BusinessUnits.AsNoTracking()
               .FirstOrDefault();

            _tenantSetting = dbContext.BusinessUnits.AsNoTracking()
                .Select(t => new TenantConfigurationOptions
                {
                    TenantId = t.Id,
                    Name = t.Name,
                }).ToDictionary(k => k.TenantId, v => v!);

            _userContext = userContext;
        }

        public IDisposable BeginScope(int tenantId)
        {
            var tenant = _tenantSetting.FirstOrDefault(x => x.Key == tenantId);

            var tenantHolder = new TenantContext.TenantContextHolder()
            {
                TenantId = tenant.Value?.TenantId,
                Tenant = tenant.Value?.Name
            };

            return TenantContext.BeginScope(tenantHolder);
        }

        public override string? ToString()
        {
            return TenantName;
        }

        public bool Validate(int tenantId)
        {
            //bool isValid = _tenantSetting.ContainsKey(tenantId);
            
            //if(!isValid)
            //    return isValid;

            if (!_userContext.Tenants.Contains(tenantId))
                return false;

            return true;
        }
    }
}
