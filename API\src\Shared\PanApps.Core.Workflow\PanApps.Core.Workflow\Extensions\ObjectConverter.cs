﻿using System.Text.Json;

namespace PanApps.Core.Workflow
{
    public static class ObjectConverter
    {
        public static T ConvertTo<T>(this object value)
        {
            if (value == null)
                return default;

            if (value is T)
                return (T)value;

            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (InvalidCastException)
            {
                return default(T);
            }
        }

        public static T CloneObj<T>(this T source)
        {
            // Don't serialize a null object, simply return the default for that object
            if (ReferenceEquals(source, null)) return default;

            // initialize inner objects individually
            // for example in default constructor some list property initialized with some values,
            // but in 'source' these items are cleaned -
            // without ObjectCreationHandling.Replace default constructor values will be added to result

            return JsonSerializer.Deserialize<T>(JsonSerializer.Serialize(source));

        }
    }
}
