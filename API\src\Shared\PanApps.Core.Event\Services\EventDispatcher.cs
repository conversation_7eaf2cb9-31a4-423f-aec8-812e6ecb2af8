﻿using MediatR;

namespace PanApps.Core.Event.Events
{
    public class EventDispatcher
    {
        private readonly IMediator _mediator;

        public EventDispatcher(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task Publish<TEvent>(TEvent eventInstance) where TEvent : INotification
        {
            await _mediator.Publish(eventInstance);
        }
    }
}
