﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public class UserInvitationConfiguration : IEntityTypeConfiguration<UserInvitation>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserInvitation> builder)
        {
            builder.ToTable("user_invitations");
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedOnAdd();

            builder.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .HasPrincipalKey(e => e.Id);

            builder.Property(c => c.InvitationURL).IsRequired().HasMaxLength(500);
            builder.Property(c => c.Token).IsRequired().HasMaxLength(256);
            builder.Property(c => c.Email).IsRequired().HasMaxLength(100);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new UserInvitationConfiguration());
        }
    }
}
