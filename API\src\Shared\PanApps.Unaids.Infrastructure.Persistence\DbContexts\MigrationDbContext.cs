﻿using Microsoft.EntityFrameworkCore;
using PanApps.Unaids.Domain;
using PanApps.Model.Administration;
using PanApps.Unaids.Domain.System;
using PanApps.Unaids.Domain.TenantManagement;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public class MigrationDbContext(DbContextOptions<MigrationDbContext> options) : DbContext(options)
    {
        #region system administration...
        public DbSet<User> Users { get; set; }
        public DbSet<UserInvitation> UserInvitations { get; set; }
        public DbSet<UserGroups> UserGroups { get; set; }
        public DbSet<UserGroupMapping> UserGroupMappings { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<Menu> Menus { get; set; }
        public DbSet<MenuDetail> MenuDetails { get; set; }
        public DbSet<Module> Modules { get; set; }
        public DbSet<ModuleGroup> ModuleGroups { get; set; }
        public DbSet<StandardSchema> StandardSchemas { get; set; }
        public DbSet<Program> Programs { get; set; }
        //public DbSet<ProgramSetup> ProgramSetup { get; set; }
        public DbSet<ProgramStage> ProgramStages { get; set; }
        public DbSet<Sequence> Sequences { get; set; }
        public DbSet<LookUpInfo> LookUpInfo { get; set; }
        public DbSet<LookUpType> LookUpTypes { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<RoleMenuMapping> RoleMenuMappings { get; set; }
        public DbSet<RoleMenuMaster> RoleMenuMasters { get; set; }
        public DbSet<RolePermissionMapping> RolePermissionMappings { get; set; }
        public DbSet<OBSSetting> OBSSettings { get; set; }
        public DbSet<BusinessUnit> BusinessUnits { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<PasswordHistory> PasswordHistories { get; set; }
        public DbSet<WorkflowDefinition> WorkflowDefinitions { get; set; }
        public DbSet<WorkflowStep> WorkflowSteps { get; set; }
        public DbSet<WorkflowTransition> WorkflowTransitions { get; set; }
        //public DbSet<WorkflowPermission> WorkflowPermissions { get; set; }
        //public DbSet<WorkflowInstance> WorkflowInstances { get; set; }
        public DbSet<WorkflowHistory> WorkflowHistories { get; set; }
        public DbSet<WorkflowReturnLog> WorkflowReturnLogs { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<Language> Languages { get; set; }
        public DbSet<AppEvent> AppEvents { get; set; }
        #endregion

        #region common...
        public DbSet<DocumentStore> DocumentStores { get; set; }
        public DbSet<DocumentStoreDetails> DocumentStoreDetails { get; set; }
        #endregion

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.RegisterModels();

            // add shadow properties
            builder.AddShadowProperties();

            // Make referential delete behaviour restrict instead of cascade for everything
            foreach (var relationship in builder.Model.GetEntityTypes()
                         .SelectMany(x => x.GetForeignKeys()))
            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;
            }

            base.OnModelCreating(builder);
        }
    }
}
