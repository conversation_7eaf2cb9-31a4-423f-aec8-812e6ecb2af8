﻿using Microsoft.AspNetCore.Authorization;

namespace PanApps.Authorization.Shared
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = false)]
    public class PrivilegedAccessAttribute: AuthorizeAttribute
    {
        /// <summary>
        /// Creates a new instance of <see cref="AuthorizeAttribute"/> class.
        /// </summary>
        /// <param name="permissions">A list of permissions to authorize</param>
        public PrivilegedAccessAttribute(params string[] permissions)
        {
            Policy = string.Join(",", permissions);
        }
    }
}
