﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base.Contracts;
using PanApps.Core.Event.DTOs;
using PanApps.Core.Workflow;
using PanApps.Unaids.Domain.Common;
using PanApps.Unaids.Infrastructure.Persistence;
using System.Text.Json;

namespace PanApps.Core.Event
{
    public class WorkflowEventHandler<TEvent> : INotificationHandler<TEvent> where TEvent : WorkflowTriggeredEvent<object>
    {
        private readonly SystemAdminDbContext _dbContext;
        private readonly IWorkflowRegistry _workflowRegistry;
        private readonly IUserContext _userContext;

        public WorkflowEventHandler(SystemAdminDbContext dbContext, IWorkflowRegistry workflowRegistry, IUserContext userContext)
        {
            _dbContext = dbContext;
            _workflowRegistry = workflowRegistry;
            _userContext = userContext;
        }

        public async Task Handle(TEvent notification, CancellationToken cancellationToken)
        {
            // Check if event exists in AppEvents table
            var appEvent = await _dbContext.AppEvents.AsNoTracking()
                .FirstOrDefaultAsync(e => e.Name == notification.EventName);

            // Exit if the event doesn't exist
            if (appEvent == null)
            {
                return;
            }

            // Proceed with workflow execution
            if (notification.Entity is BaseApprovalEntity entity && appEvent.EventData != null)
            {
                var jsonString = appEvent.EventData.RootElement.GetRawText();
                var eventData = JsonSerializer.Deserialize<EventDataDTO>(jsonString);

                if (notification.Action == EntityState.Added)
                {
                    var result = await _workflowRegistry.StartWorkflow(eventData.WorkflowName, _userContext.Id);
                    entity.WorkflowInstanceId = result.WorkflowInstance?.WorkflowInstanceId;
                }
                //else if (notification.Action == EntityState.Modified && entity.WorkflowInstanceId != null)
                //{
                //    var instanceId = Guid.Parse(entity.WorkflowInstanceId?.ToString() ?? "");
                //    await _workflowRegistry.TriggerWorkflow(instanceId, _userContext.Id, _userContext.Id);
                //}
            }
        }
    }
}
