﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using PanApps.Core.Base;
using PanApps.PanGMS.Domain;
using PanApps.PanGMS.Infrastructure.Persistence;

namespace PanApps.Core.DocumentRepository
{
    public class LocalDocumentRepository(IUnitOfWork<CommonDbContext> unitOfWork, IMapper mapper, ISequenceRepository sequenceRepository, IConfiguration configuration) 
        : BaseDocumentRepository(unitOfWork, mapper, sequenceRepository), IDocumentRepository
    {
        #region read file...
        /// <summary>
        /// Read the file...
        /// </summary>
        /// <param name="documentStoreDetail"></param>
        /// <returns></returns>
        public override async Task<MemoryStream> ReadFile(DocumentStoreDetails documentStoreDetail)
        {
            var filePath = documentStoreDetail.UploadedPath ?? "";

            var memory = new MemoryStream();
            using (var stream = new FileStream(filePath, FileMode.Open ))
            {
                await stream.CopyToAsync(memory);
            }
            memory.Position = 0;

            return memory;
        }
        #endregion

        /// <summary>
        /// Write file to the local file storage...
        /// </summary>
        /// <param name="file"></param>
        /// <param name="documentInfo"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public override async Task<DocumentStoreDetails> WriteFile(Stream file, DocumentRequestDto documentInfo, string fileName)
        {
            // #. get the blob client...
            var basePath = configuration.GetSection("Storage:FileUploadPath").Value;

            var path = fileName;
            if (!string.IsNullOrEmpty(documentInfo.Folder))
                path = Path.Join(documentInfo.Folder, fileName);

            var filePath = Path.Join(basePath, path);

            string directory = Path.GetDirectoryName(filePath);

            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                file.Seek(0, SeekOrigin.Begin);
                await file.CopyToAsync(fileStream);
            }

            var docDetail = mapper.Map<DocumentRequestDto, DocumentStoreDetails>(documentInfo);
            docDetail.UploadedName = fileName;
            docDetail.UploadedPath = filePath;

            return docDetail;
        }
    }
}
