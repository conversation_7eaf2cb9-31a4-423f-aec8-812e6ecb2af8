﻿using Microsoft.EntityFrameworkCore;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public static class ConfigurationExtensions
    {
        public static void RegisterModels(this ModelBuilder modelBuilder)
        {
            var entityConfigurations = typeof(Persistence).Assembly
                .GetTypes()
                .Where(t => t.IsAssignableTo(typeof(IEntityConfiguration)) && !t.IsAbstract && !t.IsInterface)
                .Select(Activator.CreateInstance)
                .Cast<IEntityConfiguration>();

            foreach (var entity in entityConfigurations)
            {
                entity.ConfigureEntity(modelBuilder);
            }

            modelBuilder.HasDefaultSchema("pan_gms");
        }
    }
}
