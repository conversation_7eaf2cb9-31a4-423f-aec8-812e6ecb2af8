﻿namespace PanApps.Core.Workflow
{
    public class WorkflowExecutor(IWorkflowTransition workflowTransition): IWorkflowExecutor
    {
        /// <summary>
        /// execute workflow step transition
        /// </summary>
        /// <param name="workflowInstance"></param>
        /// <param name="nextStepId"></param>
        /// <param name="userId"></param>
        /// <param name="variables"></param>
        /// <returns></returns>
        public async Task<WorkflowExecutorResult> Execute(WorkflowInstanceDTO workflowInstance, int nextStepId, int userId, Variables variables)
        {
            return await workflowTransition.Execute(workflowInstance, nextStepId, userId, variables);
        }
    }
}
