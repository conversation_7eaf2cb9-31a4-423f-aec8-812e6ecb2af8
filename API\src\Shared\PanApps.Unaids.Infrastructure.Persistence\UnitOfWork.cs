﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;
using PanApps.Core.DataAccess.Dynamic;
using System.Reflection;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class UnitOfWork<TContext> : IUnitOfWork<TContext> where TContext : DbContext
    {
        private readonly TContext _context;
        private readonly IServiceProvider _serviceProvider;
        private readonly Dictionary<Type, object> _repositories = new();

        public UnitOfWork(TContext context, IServiceProvider provider)
        {
            _context = context;
            _serviceProvider = provider;
        }

        /// <summary>
        /// Get derived repository within a Unit of Work
        /// </summary>
        /// <typeparam name="TRepository"></typeparam>
        /// <returns></returns>
        public TRepository Repository<TRepository>() where TRepository : class
        {
            return _serviceProvider.GetRequiredService<TRepository>();
        }

        public IDynamicRepository<T> DynamicRepository<T>() where T: class
        {
            return new DynamicRepository<T, TContext>(_context);
        }

        /// <summary>
        /// Commits all changes made within the unit of work
        /// </summary>
        /// <returns></returns>
        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            Dispose(true);
            // ReSharper disable once GCSuppressFinalizeForTypeWithoutDestructor
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context.Dispose();
            }
        }
    }
}
