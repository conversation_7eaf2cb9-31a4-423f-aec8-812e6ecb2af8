using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class LookUpInfoConfiguration : IEntityTypeConfiguration<LookUpInfo>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<LookUpInfo> builder)
        {
            builder.ToTable("look_up_info");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Code).IsRequired().HasMaxLength(50);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.SortOrder).IsRequired(false);
            builder.Property(e => e.IsSystemDefined).IsRequired(false);

            // Relationship with LookUpType
            builder.HasOne(e => e.LookUpType)
                   .WithMany()
                   .HasForeignKey(e => e.LookUpTypeId)
                   .HasPrincipalKey(e => e.Id);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new LookUpInfoConfiguration());
        }
    }
}
