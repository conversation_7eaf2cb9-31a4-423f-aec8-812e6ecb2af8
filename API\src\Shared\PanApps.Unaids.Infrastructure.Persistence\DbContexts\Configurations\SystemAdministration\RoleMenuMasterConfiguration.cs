using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Model.Administration;
using PanApps.Unaids.Infrastructure.Persistence;

namespace PanApps.Infrastructure.Persistence
{
    public sealed class RoleMenuMasterConfiguration : IEntityTypeConfiguration<RoleMenuMaster>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<RoleMenuMaster> builder)
        {
            builder.ToTable("role_menu_masters");

            // Configure primary key
            builder.HasKey(e => e.Id);

            // Configure BusinessUnitId
            builder.Property(e => e.BusinessUnitId)
                .IsRequired();

            // Configure RoleId with foreign key to Role
            builder.Property(e => e.RoleId)
                .IsRequired();

            builder.HasOne(e => e.Role)
                .WithMany()
                .HasForeignKey(e => e.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure ModuleId with foreign key to Module
            builder.Property(e => e.ModuleId)
                .IsRequired();

            builder.HasOne(e => e.Module)
                .WithMany()
                .HasForeignKey(e => e.ModuleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure IsSystemDefined
            builder.Property(e => e.IsSystemDefined)
                .IsRequired(false);

            // Configure RoleMenuMapping (navigation property)
            builder.HasMany(e => e.RoleMenuMapping)
                .WithOne()
                .HasForeignKey("RoleMenuMasterId"); // Assuming this foreign key exists in RoleMenuMapping
        }
        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RoleMenuMasterConfiguration());
        }
    }
}
