﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PanApps.Unaids.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "slug",
                schema: "pan_gms",
                table: "models",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "taxonomy_id",
                schema: "pan_gms",
                table: "models",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "navigation_model_id",
                schema: "pan_gms",
                table: "model_data_elements",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AddColumn<string>(
                name: "options",
                schema: "pan_gms",
                table: "model_data_elements",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "slug",
                schema: "pan_gms",
                table: "models");

            migrationBuilder.DropColumn(
                name: "taxonomy_id",
                schema: "pan_gms",
                table: "models");

            migrationBuilder.DropColumn(
                name: "options",
                schema: "pan_gms",
                table: "model_data_elements");

            migrationBuilder.AlterColumn<int>(
                name: "navigation_model_id",
                schema: "pan_gms",
                table: "model_data_elements",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);
        }
    }
}
