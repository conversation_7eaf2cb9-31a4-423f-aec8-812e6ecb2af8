﻿using System;
using System.Text.Json;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PanApps.Unaids.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "pan_gms");

            migrationBuilder.CreateTable(
                name: "amendment_data",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_amendment_data", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "applicationdata",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_applicationdata", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "call_for_grants",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    activity_type_id = table.Column<int>(type: "integer", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_call_for_grants", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "closure_data",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_closure_data", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "concepts",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    slug = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    synonym = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    subject_to_change = table.Column<short>(type: "smallint", nullable: true),
                    custom_schema_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_concepts", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "countries",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    description = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_countries", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "currencies",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    symbol = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_currencies", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "document_sequence_info",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    base_entity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    transaction_id = table.Column<int>(type: "integer", nullable: false),
                    entity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    next_sequence_number = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_document_sequence_info", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "document_store",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    reference_no = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    transaction_id = table.Column<int>(type: "integer", nullable: true),
                    service_id = table.Column<int>(type: "integer", nullable: true),
                    module_id = table.Column<int>(type: "integer", nullable: true),
                    business_unit_id = table.Column<int>(type: "integer", nullable: true),
                    remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_document_store", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fiscal_years",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    fiscal_year_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    start_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fiscal_years", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "grant_types",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_grant_types", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "look_up_types",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    field_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_look_up_types", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "model_schemas",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    base_schema = table.Column<JsonDocument>(type: "json", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_model_schemas", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "module_groups",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    sort_order = table.Column<int>(type: "integer", nullable: true),
                    icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    dashboard_url = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    tile_menu_pos = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_module_groups", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "opportunitydata",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_opportunitydata", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "performance_report_data",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_performance_report_data", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "programs",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    business_unit_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_programs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "review_data",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    comments = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    form_data = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_review_data", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "role_info",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    level = table.Column<decimal>(type: "numeric", nullable: false),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role_info", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sequence_info",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    table_name = table.Column<string>(type: "text", nullable: false),
                    minimum_value = table.Column<int>(type: "integer", nullable: false),
                    maximum_value = table.Column<int>(type: "integer", nullable: false),
                    increment_by = table.Column<int>(type: "integer", nullable: false),
                    next_value = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sequence_info", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "user_groups",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    email = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_groups", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "workflow_definitions",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_definitions", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "workflow_histories",
                schema: "pan_gms",
                columns: table => new
                {
                    workflow_history_id = table.Column<Guid>(type: "uuid", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: false),
                    from_step_id = table.Column<int>(type: "integer", nullable: true),
                    to_step_id = table.Column<int>(type: "integer", nullable: true),
                    activity = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    execution_status = table.Column<short>(type: "smallint", nullable: true),
                    ip_address = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    comments = table.Column<string>(type: "text", nullable: true),
                    json_data = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_histories", x => x.workflow_history_id);
                });

            migrationBuilder.CreateTable(
                name: "workflow_return_logs",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: false),
                    current_step_id = table.Column<int>(type: "integer", nullable: true),
                    revision_no = table.Column<int>(type: "integer", nullable: true),
                    return_status = table.Column<int>(type: "integer", nullable: true),
                    comment = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_return_logs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "opportunities",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    title = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    ttl_id = table.Column<int>(type: "integer", nullable: true),
                    team_member_ids = table.Column<int?[]>(type: "integer[]", nullable: false),
                    call_id = table.Column<int>(type: "integer", nullable: true),
                    call_for_grants_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_opportunities", x => x.id);
                    table.ForeignKey(
                        name: "fk_opportunities_call_for_grants_call_for_grants_id",
                        column: x => x.call_for_grants_id,
                        principalSchema: "pan_gms",
                        principalTable: "call_for_grants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "document_store_details",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    document_store_id = table.Column<int>(type: "integer", nullable: false),
                    title = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    file_name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    uploaded_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    uploaded_path = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    uploaded_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    file_size = table.Column<int>(type: "integer", nullable: true),
                    user_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_document_store_details", x => x.id);
                    table.ForeignKey(
                        name: "fk_document_store_details_document_stores_document_store_id",
                        column: x => x.document_store_id,
                        principalSchema: "pan_gms",
                        principalTable: "document_store",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "look_up_info",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    look_up_type_id = table.Column<int>(type: "integer", nullable: false),
                    sort_order = table.Column<decimal>(type: "numeric", nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_look_up_info", x => x.id);
                    table.ForeignKey(
                        name: "fk_look_up_info_look_up_types_look_up_type_id",
                        column: x => x.look_up_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "modules",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    module_group_id = table.Column<int>(type: "integer", nullable: false),
                    sort_order = table.Column<int>(type: "integer", nullable: true),
                    icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    dashboard_url = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_modules", x => x.id);
                    table.ForeignKey(
                        name: "fk_modules_module_groups_module_group_id",
                        column: x => x.module_group_id,
                        principalSchema: "pan_gms",
                        principalTable: "module_groups",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_concepts",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    concept_id = table.Column<int>(type: "integer", nullable: false),
                    display_name = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_concepts", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_concepts_concepts_concept_id",
                        column: x => x.concept_id,
                        principalSchema: "pan_gms",
                        principalTable: "concepts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_concepts_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_hierarchy_categories",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    category_id = table.Column<int>(type: "integer", nullable: false),
                    entity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    display_name = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_hierarchy_categories", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_categories_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "role_permission_mappings",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    role_id = table.Column<int>(type: "integer", nullable: false),
                    menu_add = table.Column<int>(type: "integer", nullable: false),
                    menu_modify = table.Column<int>(type: "integer", nullable: false),
                    menu_cancel = table.Column<int>(type: "integer", nullable: false),
                    menu_view = table.Column<int>(type: "integer", nullable: false),
                    menu_print = table.Column<int>(type: "integer", nullable: false),
                    menu_re_print = table.Column<int>(type: "integer", nullable: false),
                    menu_delete = table.Column<int>(type: "integer", nullable: false),
                    menu_process = table.Column<int>(type: "integer", nullable: false),
                    menu_approve = table.Column<int>(type: "integer", nullable: false),
                    menu_pre_dated_entry = table.Column<int>(type: "integer", nullable: false),
                    menu_import = table.Column<int>(type: "integer", nullable: false),
                    menu_export = table.Column<int>(type: "integer", nullable: false),
                    menu_validation = table.Column<int>(type: "integer", nullable: false),
                    menu_correct = table.Column<int>(type: "integer", nullable: false),
                    menu_bulk_import = table.Column<int>(type: "integer", nullable: false),
                    menu_bulk_update = table.Column<int>(type: "integer", nullable: false),
                    menu_bulk_delete = table.Column<int>(type: "integer", nullable: false),
                    menu_export_record = table.Column<int>(type: "integer", nullable: false),
                    restricted_view = table.Column<int>(type: "integer", nullable: false),
                    menu_json_edit = table.Column<int>(type: "integer", nullable: false),
                    menu_special1 = table.Column<int>(type: "integer", nullable: false),
                    menu_special2 = table.Column<int>(type: "integer", nullable: false),
                    menu_special3 = table.Column<int>(type: "integer", nullable: false),
                    menu_special4 = table.Column<int>(type: "integer", nullable: false),
                    menu_special5 = table.Column<int>(type: "integer", nullable: false),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    model_definition_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role_permission_mappings", x => x.id);
                    table.ForeignKey(
                        name: "fk_role_permission_mappings_roles_role_id",
                        column: x => x.role_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "user_info",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    user_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    login_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    role_id = table.Column<int>(type: "integer", nullable: true),
                    normalized_login_name = table.Column<string>(type: "text", nullable: false),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    normalized_email = table.Column<string>(type: "text", nullable: false),
                    password_hash = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    effective_from = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    effective_to = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false),
                    is_locked = table.Column<bool>(type: "boolean", nullable: false),
                    status_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_info", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_info_role_info_role_id",
                        column: x => x.role_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "user_permissions",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    user_id = table.Column<int>(type: "integer", nullable: false),
                    business_unit_id = table.Column<int>(type: "integer", nullable: false),
                    app_id = table.Column<int>(type: "integer", nullable: true),
                    role_id = table.Column<int>(type: "integer", nullable: false),
                    module_group_id = table.Column<int>(type: "integer", nullable: true),
                    module_id = table.Column<int>(type: "integer", nullable: false),
                    effective_from = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    effective_to = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    business_unit_value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    user_same_level_flag = table.Column<bool>(type: "boolean", nullable: false),
                    permission_flag = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_permissions", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_permissions_roles_role_id",
                        column: x => x.role_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "user_group_mapping",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    user_groups_id = table.Column<int>(type: "integer", nullable: false),
                    user_id = table.Column<int>(type: "integer", nullable: false),
                    default_user_group = table.Column<short>(type: "smallint", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_group_mapping", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_group_mapping_user_groups_user_groups_id",
                        column: x => x.user_groups_id,
                        principalSchema: "pan_gms",
                        principalTable: "user_groups",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "workflow_instances",
                schema: "pan_gms",
                columns: table => new
                {
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: false),
                    workflow_definition_id = table.Column<int>(type: "integer", nullable: false),
                    correlation_id = table.Column<Guid>(type: "uuid", nullable: true),
                    previous_step_id = table.Column<int>(type: "integer", nullable: true),
                    current_step_id = table.Column<int>(type: "integer", nullable: true),
                    execution_status = table.Column<short>(type: "smallint", nullable: true),
                    is_initial_step = table.Column<short>(type: "smallint", nullable: true),
                    is_completed = table.Column<short>(type: "smallint", nullable: true),
                    is_returned = table.Column<short>(type: "smallint", nullable: true),
                    revision_no = table.Column<int>(type: "integer", nullable: true),
                    current_status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    workflow_data = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_on = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_on = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_instances", x => x.workflow_instance_id);
                    table.ForeignKey(
                        name: "fk_workflow_instances_workflow_definitions_workflow_definition",
                        column: x => x.workflow_definition_id,
                        principalSchema: "pan_gms",
                        principalTable: "workflow_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "workflow_permissions",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    workflow_definition_id = table.Column<int>(type: "integer", nullable: false),
                    role_id = table.Column<int>(type: "integer", nullable: false),
                    step_transitions = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_permissions", x => x.id);
                    table.ForeignKey(
                        name: "fk_workflow_permissions_role_info_role_id",
                        column: x => x.role_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_workflow_permissions_workflow_definitions_workflow_definiti",
                        column: x => x.workflow_definition_id,
                        principalSchema: "pan_gms",
                        principalTable: "workflow_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "workflow_steps",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    workflow_definition_id = table.Column<int>(type: "integer", nullable: false),
                    step_no = table.Column<int>(type: "integer", nullable: false),
                    step_code = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: false),
                    step_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    display_name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    action = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    sort_order = table.Column<int>(type: "integer", nullable: false),
                    is_forward_flow = table.Column<short>(type: "smallint", nullable: true),
                    allow_rollback = table.Column<short>(type: "smallint", nullable: true),
                    lock_status = table.Column<short>(type: "smallint", nullable: true),
                    can_pause = table.Column<short>(type: "smallint", nullable: true),
                    return_status = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    trigger_service = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_workflow_steps", x => x.id);
                    table.ForeignKey(
                        name: "fk_workflow_steps_workflow_definitions_workflow_definition_id",
                        column: x => x.workflow_definition_id,
                        principalSchema: "pan_gms",
                        principalTable: "workflow_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "applications",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    opportunity_id = table.Column<int>(type: "integer", nullable: true),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    ttl_id = table.Column<int>(type: "integer", nullable: true),
                    team_member_ids = table.Column<int?[]>(type: "integer[]", nullable: false),
                    reviewer_ids = table.Column<int?[]>(type: "integer[]", nullable: false),
                    call_id = table.Column<int>(type: "integer", nullable: true),
                    currency_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_applications", x => x.id);
                    table.ForeignKey(
                        name: "fk_applications_call_for_grants_call_id",
                        column: x => x.call_id,
                        principalSchema: "pan_gms",
                        principalTable: "call_for_grants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_applications_currencies_currency_id",
                        column: x => x.currency_id,
                        principalSchema: "pan_gms",
                        principalTable: "currencies",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_applications_opportunities_opportunity_id",
                        column: x => x.opportunity_id,
                        principalSchema: "pan_gms",
                        principalTable: "opportunities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "categories",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    category_type_id = table.Column<int>(type: "integer", nullable: true),
                    parent_category_id = table.Column<int>(type: "integer", nullable: true),
                    custom_schema_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_categories", x => x.id);
                    table.ForeignKey(
                        name: "fk_categories_look_up_info_category_type_id",
                        column: x => x.category_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "country_details",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    country_id = table.Column<int>(type: "integer", nullable: false),
                    fiscal_year_id = table.Column<int>(type: "integer", nullable: false),
                    region_id = table.Column<int>(type: "integer", nullable: true),
                    income_group_id = table.Column<int>(type: "integer", nullable: true),
                    fcs_id = table.Column<int>(type: "integer", nullable: true),
                    sids_id = table.Column<int>(type: "integer", nullable: true),
                    lending_category_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_country_details", x => x.id);
                    table.ForeignKey(
                        name: "fk_country_details_countries_country_id",
                        column: x => x.country_id,
                        principalSchema: "pan_gms",
                        principalTable: "countries",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_fiscal_years_fiscal_year_id",
                        column: x => x.fiscal_year_id,
                        principalSchema: "pan_gms",
                        principalTable: "fiscal_years",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_look_up_info_fcs_id",
                        column: x => x.fcs_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_look_up_info_income_group_id",
                        column: x => x.income_group_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_look_up_info_lending_category_id",
                        column: x => x.lending_category_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_look_up_info_region_id",
                        column: x => x.region_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_country_details_look_up_info_sids_id",
                        column: x => x.sids_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "data_elements",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true),
                    concept_id = table.Column<int>(type: "integer", nullable: true),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    slug = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    is_required = table.Column<short>(type: "smallint", nullable: true),
                    data_type_id = table.Column<int>(type: "integer", nullable: true),
                    max_length = table.Column<int>(type: "integer", nullable: true),
                    format = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    internal_id = table.Column<Guid>(type: "uuid", nullable: false),
                    model_references = table.Column<string[]>(type: "text[]", nullable: true),
                    data_group_name = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    parent_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_data_elements", x => x.id);
                    table.ForeignKey(
                        name: "fk_data_elements_concepts_concept_id",
                        column: x => x.concept_id,
                        principalSchema: "pan_gms",
                        principalTable: "concepts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_data_elements_look_up_info_data_type_id",
                        column: x => x.data_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "events",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    event_type_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_events", x => x.id);
                    table.ForeignKey(
                        name: "fk_events_look_up_info_event_type_id",
                        column: x => x.event_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "frameworks",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    framework_type_id = table.Column<int>(type: "integer", nullable: false),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_frameworks", x => x.id);
                    table.ForeignKey(
                        name: "fk_frameworks_look_up_info_framework_type_id",
                        column: x => x.framework_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "local_data_elements",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true),
                    concept_id = table.Column<int>(type: "integer", nullable: true),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    slug = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    is_required = table.Column<short>(type: "smallint", nullable: true),
                    data_type_id = table.Column<int>(type: "integer", nullable: true),
                    max_length = table.Column<int>(type: "integer", nullable: true),
                    format = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    internal_id = table.Column<Guid>(type: "uuid", nullable: false),
                    model_references = table.Column<string[]>(type: "text[]", nullable: true),
                    data_group_name = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    parent_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_local_data_elements", x => x.id);
                    table.ForeignKey(
                        name: "fk_local_data_elements_concepts_concept_id",
                        column: x => x.concept_id,
                        principalSchema: "pan_gms",
                        principalTable: "concepts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_local_data_elements_look_up_info_data_type_id",
                        column: x => x.data_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_local_data_elements_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "obs_settings",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    obs_type_id = table.Column<int>(type: "integer", nullable: false),
                    parent_obs_type_id = table.Column<int>(type: "integer", nullable: true),
                    code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    is_accounting_unit = table.Column<short>(type: "smallint", nullable: false),
                    is_expense_tracking_unit = table.Column<short>(type: "smallint", nullable: true),
                    level = table.Column<int>(type: "integer", nullable: false),
                    hierarchy = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    schema_definition_id = table.Column<int>(type: "integer", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_obs_settings", x => x.id);
                    table.ForeignKey(
                        name: "fk_obs_settings_look_up_info_obs_type_id",
                        column: x => x.obs_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_obs_settings_look_up_info_parent_obs_type_id",
                        column: x => x.parent_obs_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_stage",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    grant_stage_id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    sort_order = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_stage", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_stage_look_up_info_grant_stage_id",
                        column: x => x.grant_stage_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_stage_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "menu_config",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    parent_menu_id = table.Column<int>(type: "integer", nullable: true),
                    title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    component_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    module_id = table.Column<int>(type: "integer", nullable: true),
                    menu_type = table.Column<int>(type: "integer", nullable: false),
                    url = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    sort_order = table.Column<int>(type: "integer", nullable: true),
                    remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    is_hidden = table.Column<short>(type: "smallint", nullable: true),
                    icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    path = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    configuration_settings = table.Column<string>(type: "json", nullable: true),
                    template_id = table.Column<int>(type: "integer", nullable: true),
                    object_type = table.Column<int>(type: "integer", nullable: true),
                    service_component_id = table.Column<int>(type: "integer", nullable: true),
                    controller_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    area = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    controller = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_menu_config", x => x.id);
                    table.ForeignKey(
                        name: "fk_menu_config_menu_config_parent_menu_id",
                        column: x => x.parent_menu_id,
                        principalSchema: "pan_gms",
                        principalTable: "menu_config",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_menu_config_modules_module_id",
                        column: x => x.module_id,
                        principalSchema: "pan_gms",
                        principalTable: "modules",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "role_menu_masters",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    business_unit_id = table.Column<int>(type: "integer", nullable: false),
                    role_id = table.Column<int>(type: "integer", nullable: false),
                    module_id = table.Column<int>(type: "integer", nullable: false),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    equal_role_level_flag = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role_menu_masters", x => x.id);
                    table.ForeignKey(
                        name: "fk_role_menu_masters_modules_module_id",
                        column: x => x.module_id,
                        principalSchema: "pan_gms",
                        principalTable: "modules",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_role_menu_masters_roles_role_id",
                        column: x => x.role_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "settings",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    module_id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    criteria = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    data_type_id = table.Column<int>(type: "integer", nullable: false),
                    value = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    group = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    schema_definition_id = table.Column<int>(type: "integer", nullable: true),
                    remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    business_rules = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_settings", x => x.id);
                    table.ForeignKey(
                        name: "fk_settings_look_up_info_data_type_id",
                        column: x => x.data_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_settings_modules_module_id",
                        column: x => x.module_id,
                        principalSchema: "pan_gms",
                        principalTable: "modules",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_hierarchy",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    category_id = table.Column<int>(type: "integer", nullable: false),
                    element_id = table.Column<int>(type: "integer", nullable: false),
                    level = table.Column<int>(type: "integer", nullable: true),
                    parent_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_hierarchy", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_program_hierarchy_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_hierarchy_categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_program_hierarchy_parent_id",
                        column: x => x.parent_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_hierarchy",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_hierarchy_levels",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: true),
                    program_hierarchy_element_id = table.Column<int>(type: "integer", nullable: false),
                    level = table.Column<int>(type: "integer", nullable: true),
                    parent_level_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_hierarchy_levels", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_levels_program_hierarchy_elements_program",
                        column: x => x.program_hierarchy_element_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_hierarchy_categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_levels_program_hierarchy_levels_parent_le",
                        column: x => x.parent_level_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_hierarchy_levels",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_hierarchy_levels_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "password_histories",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    user_id = table.Column<int>(type: "integer", nullable: false),
                    password_hash = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_password_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_password_histories_users_user_id",
                        column: x => x.user_id,
                        principalSchema: "pan_gms",
                        principalTable: "user_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "user_invitations",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    user_id = table.Column<int>(type: "integer", nullable: false),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    invitation_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    token = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    expire_on = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    status_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_invitations", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_invitations_users_user_id",
                        column: x => x.user_id,
                        principalSchema: "pan_gms",
                        principalTable: "user_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "amendments",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    application_id = table.Column<int>(type: "integer", nullable: false),
                    call_id = table.Column<int>(type: "integer", nullable: false),
                    amr_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    amr_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    revised_cost = table.Column<decimal>(type: "numeric", nullable: true),
                    revised_end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_amendments", x => x.id);
                    table.ForeignKey(
                        name: "fk_amendments_applications_application_id",
                        column: x => x.application_id,
                        principalSchema: "pan_gms",
                        principalTable: "applications",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "closure",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    application_id = table.Column<int>(type: "integer", nullable: false),
                    call_id = table.Column<int>(type: "integer", nullable: false),
                    mu_number = table.Column<int>(type: "integer", nullable: true),
                    acr_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_closure", x => x.id);
                    table.ForeignKey(
                        name: "fk_closure_applications_application_id",
                        column: x => x.application_id,
                        principalSchema: "pan_gms",
                        principalTable: "applications",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "performance_reports",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    application_id = table.Column<int>(type: "integer", nullable: false),
                    notes = table.Column<string>(type: "text", nullable: true),
                    call_id = table.Column<int>(type: "integer", nullable: true),
                    pir_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    pir_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    pir_period = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_performance_reports", x => x.id);
                    table.ForeignKey(
                        name: "fk_performance_reports_applications_application_id",
                        column: x => x.application_id,
                        principalSchema: "pan_gms",
                        principalTable: "applications",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "reviews",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    application_id = table.Column<int>(type: "integer", nullable: true),
                    reviewer_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_reviews", x => x.id);
                    table.ForeignKey(
                        name: "fk_reviews_applications_application_id",
                        column: x => x.application_id,
                        principalSchema: "pan_gms",
                        principalTable: "applications",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_reviews_users_reviewer_id",
                        column: x => x.reviewer_id,
                        principalSchema: "pan_gms",
                        principalTable: "user_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "category_options",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    category_id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    parent_option_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    taxonomy_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_category_options", x => x.id);
                    table.ForeignKey(
                        name: "fk_category_options_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "pan_gms",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_category_options_category_options_parent_option_id",
                        column: x => x.parent_option_id,
                        principalSchema: "pan_gms",
                        principalTable: "category_options",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_categories",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    category_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_categories", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_categories_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "pan_gms",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_categories_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "indicators",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    data_type_id = table.Column<int>(type: "integer", nullable: true),
                    format_id = table.Column<int>(type: "integer", nullable: true),
                    indicator_type_id = table.Column<int>(type: "integer", nullable: true),
                    frequency_id = table.Column<int>(type: "integer", nullable: true),
                    viz_types = table.Column<string>(type: "text", nullable: true),
                    is_calculated = table.Column<short>(type: "smallint", nullable: true),
                    measurement_type_id = table.Column<int>(type: "integer", nullable: true),
                    collection_method_id = table.Column<int>(type: "integer", nullable: true),
                    collection_agent_type_id = table.Column<int>(type: "integer", nullable: true),
                    collection_agent_name = table.Column<string>(type: "text", nullable: true),
                    parent_indicator_id = table.Column<int>(type: "integer", nullable: true),
                    reporting_type_id = table.Column<int>(type: "integer", nullable: true),
                    options = table.Column<int?[]>(type: "integer[]", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_indicators", x => x.id);
                    table.ForeignKey(
                        name: "fk_indicators_data_elements_parent_indicator_id",
                        column: x => x.parent_indicator_id,
                        principalSchema: "pan_gms",
                        principalTable: "data_elements",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_collection_agent_type_id",
                        column: x => x.collection_agent_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_collection_method_id",
                        column: x => x.collection_method_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_data_type_id",
                        column: x => x.data_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_format_id",
                        column: x => x.format_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_indicator_type_id",
                        column: x => x.indicator_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_measurement_type_id",
                        column: x => x.measurement_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_indicators_look_up_info_reporting_type_id",
                        column: x => x.reporting_type_id,
                        principalSchema: "pan_gms",
                        principalTable: "look_up_info",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_data_elements",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    display_name = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: false),
                    model_references = table.Column<string[]>(type: "text[]", nullable: true),
                    data_group_name = table.Column<string>(type: "text", nullable: true),
                    data_element_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_data_elements", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_data_elements_data_elements_data_element_id",
                        column: x => x.data_element_id,
                        principalSchema: "pan_gms",
                        principalTable: "data_elements",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_data_elements_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "framework_data",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    framework_id = table.Column<int>(type: "integer", nullable: false),
                    data = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_framework_data", x => x.id);
                    table.ForeignKey(
                        name: "fk_framework_data_frameworks_framework_id",
                        column: x => x.framework_id,
                        principalSchema: "pan_gms",
                        principalTable: "frameworks",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "framework_hierarchy",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    framework_id = table.Column<int>(type: "integer", nullable: false),
                    concept_id = table.Column<int>(type: "integer", nullable: false),
                    level = table.Column<int>(type: "integer", nullable: true),
                    parent_concept_id = table.Column<int>(type: "integer", nullable: true),
                    is_measurable = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_framework_hierarchy", x => x.id);
                    table.ForeignKey(
                        name: "fk_framework_hierarchy_concepts_concept_id",
                        column: x => x.concept_id,
                        principalSchema: "pan_gms",
                        principalTable: "concepts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_framework_hierarchy_concepts_parent_concept_id",
                        column: x => x.parent_concept_id,
                        principalSchema: "pan_gms",
                        principalTable: "concepts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_framework_hierarchy_frameworks_framework_id",
                        column: x => x.framework_id,
                        principalSchema: "pan_gms",
                        principalTable: "frameworks",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "business_units",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    obs_setting_id = table.Column<int>(type: "integer", nullable: false),
                    address = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: true),
                    pin_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    phone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    fax = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    web_site = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    parent_business_unit_id = table.Column<int>(type: "integer", nullable: true),
                    acc_obs_bunit_id = table.Column<int>(type: "integer", nullable: true),
                    tenant = table.Column<int>(type: "integer", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: false),
                    remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    level = table.Column<int>(type: "integer", nullable: false),
                    hierarchy = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    geo_coordinates = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    setting_detail = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    updated_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    logo = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_units", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_units_obs_settings_obs_setting_id",
                        column: x => x.obs_setting_id,
                        principalSchema: "pan_gms",
                        principalTable: "obs_settings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "model_headers",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    schema_id = table.Column<int>(type: "integer", nullable: false),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    entity_id = table.Column<int>(type: "integer", nullable: true),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    version = table.Column<int>(type: "integer", nullable: false),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_model_headers", x => x.id);
                    table.ForeignKey(
                        name: "fk_model_headers_model_schemas_schema_id",
                        column: x => x.schema_id,
                        principalSchema: "pan_gms",
                        principalTable: "model_schemas",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_model_headers_program_stages_entity_id",
                        column: x => x.entity_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_stage",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "menu_details",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    menu_config_id = table.Column<int>(type: "integer", nullable: false),
                    menu_add = table.Column<int>(type: "integer", nullable: true),
                    menu_modify = table.Column<int>(type: "integer", nullable: true),
                    menucancel = table.Column<int>(type: "integer", nullable: true),
                    menu_view = table.Column<int>(type: "integer", nullable: true),
                    menu_print = table.Column<int>(type: "integer", nullable: true),
                    menu_re_print = table.Column<int>(type: "integer", nullable: true),
                    menu_delete = table.Column<int>(type: "integer", nullable: false),
                    menu_process = table.Column<int>(type: "integer", nullable: true),
                    menu_approve = table.Column<int>(type: "integer", nullable: false),
                    menu_pre_dated_entry = table.Column<int>(type: "integer", nullable: false),
                    menu_import = table.Column<int>(type: "integer", nullable: false),
                    menu_export = table.Column<int>(type: "integer", nullable: false),
                    menu_validation = table.Column<int>(type: "integer", nullable: false),
                    menu_comments = table.Column<int>(type: "integer", nullable: true),
                    menu_correct = table.Column<int>(type: "integer", nullable: true),
                    menu_bulk_import = table.Column<int>(type: "integer", nullable: true),
                    menu_export_record = table.Column<int>(type: "integer", nullable: true),
                    menu_json_edit = table.Column<int>(type: "integer", nullable: true),
                    menu_restricted_view = table.Column<int>(type: "integer", nullable: true),
                    menu_special1 = table.Column<int>(type: "integer", nullable: true),
                    menu_special2 = table.Column<int>(type: "integer", nullable: true),
                    menu_special3 = table.Column<int>(type: "integer", nullable: true),
                    menu_special4 = table.Column<int>(type: "integer", nullable: true),
                    menu_special5 = table.Column<int>(type: "integer", nullable: true),
                    menu_tool_tip = table.Column<int>(type: "integer", nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    model_definition_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_menu_details", x => x.id);
                    table.ForeignKey(
                        name: "fk_menu_details_menus_menu_config_id",
                        column: x => x.menu_config_id,
                        principalSchema: "pan_gms",
                        principalTable: "menu_config",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "standardschemas",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    menu_id = table.Column<int>(type: "integer", nullable: true),
                    schema = table.Column<string>(type: "json", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_standardschemas", x => x.id);
                    table.ForeignKey(
                        name: "fk_standardschemas_menu_config_menu_id",
                        column: x => x.menu_id,
                        principalSchema: "pan_gms",
                        principalTable: "menu_config",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "role_menu_mappings",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn),
                    menu_id = table.Column<int>(type: "integer", nullable: false),
                    role_menu_master_id = table.Column<int>(type: "integer", nullable: true),
                    menu_add = table.Column<int>(type: "integer", nullable: true),
                    menu_modify = table.Column<int>(type: "integer", nullable: true),
                    menu_cancel = table.Column<int>(type: "integer", nullable: true),
                    menu_view = table.Column<int>(type: "integer", nullable: true),
                    menu_print = table.Column<int>(type: "integer", nullable: true),
                    menu_re_print = table.Column<int>(type: "integer", nullable: true),
                    menu_delete = table.Column<int>(type: "integer", nullable: true),
                    menu_process = table.Column<int>(type: "integer", nullable: true),
                    menu_approve = table.Column<int>(type: "integer", nullable: true),
                    menu_pre_dated_entry = table.Column<int>(type: "integer", nullable: true),
                    menu_import = table.Column<int>(type: "integer", nullable: true),
                    menu_export = table.Column<int>(type: "integer", nullable: true),
                    menu_validation = table.Column<int>(type: "integer", nullable: true),
                    menu_correct = table.Column<int>(type: "integer", nullable: true),
                    menu_bulk_import = table.Column<int>(type: "integer", nullable: true),
                    menu_bulk_update = table.Column<int>(type: "integer", nullable: true),
                    menu_bulk_delete = table.Column<int>(type: "integer", nullable: true),
                    menu_export_record = table.Column<int>(type: "integer", nullable: true),
                    restricted_view = table.Column<int>(type: "integer", nullable: true),
                    menu_json_edit = table.Column<int>(type: "integer", nullable: true),
                    menu_special1 = table.Column<int>(type: "integer", nullable: true),
                    menu_special2 = table.Column<int>(type: "integer", nullable: true),
                    menu_special3 = table.Column<int>(type: "integer", nullable: true),
                    menu_special4 = table.Column<int>(type: "integer", nullable: true),
                    menu_special5 = table.Column<int>(type: "integer", nullable: true),
                    is_system_defined = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_role_menu_mappings", x => x.id);
                    table.ForeignKey(
                        name: "fk_role_menu_mappings_menus_menu_id",
                        column: x => x.menu_id,
                        principalSchema: "pan_gms",
                        principalTable: "menu_config",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_role_menu_mappings_role_menu_masters_role_menu_master_id",
                        column: x => x.role_menu_master_id,
                        principalSchema: "pan_gms",
                        principalTable: "role_menu_masters",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "program_category_options",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    program_id = table.Column<int>(type: "integer", nullable: false),
                    category_option_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_program_category_options", x => x.id);
                    table.ForeignKey(
                        name: "fk_program_category_options_category_options_category_option_id",
                        column: x => x.category_option_id,
                        principalSchema: "pan_gms",
                        principalTable: "category_options",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_program_category_options_programs_program_id",
                        column: x => x.program_id,
                        principalSchema: "pan_gms",
                        principalTable: "programs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "call_program_stages",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    call_id = table.Column<int>(type: "integer", nullable: false),
                    program_stage_id = table.Column<int>(type: "integer", nullable: false),
                    from_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    form_template_id = table.Column<int>(type: "integer", nullable: true),
                    document_number_format = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    workflow_id = table.Column<int>(type: "integer", nullable: true),
                    attributes = table.Column<JsonDocument>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_call_program_stages", x => x.id);
                    table.ForeignKey(
                        name: "fk_call_program_stages_call_for_grants_call_id",
                        column: x => x.call_id,
                        principalSchema: "pan_gms",
                        principalTable: "call_for_grants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_call_program_stages_model_headers_form_template_id",
                        column: x => x.form_template_id,
                        principalSchema: "pan_gms",
                        principalTable: "model_headers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_call_program_stages_program_stages_program_stage_id",
                        column: x => x.program_stage_id,
                        principalSchema: "pan_gms",
                        principalTable: "program_stage",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_call_program_stages_workflow_definitions_workflow_id",
                        column: x => x.workflow_id,
                        principalSchema: "pan_gms",
                        principalTable: "workflow_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "model_templates",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    model_header_id = table.Column<int>(type: "integer", nullable: false),
                    language = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    template = table.Column<JsonDocument>(type: "json", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_model_templates", x => x.id);
                    table.ForeignKey(
                        name: "fk_model_templates_model_headers_model_header_id",
                        column: x => x.model_header_id,
                        principalSchema: "pan_gms",
                        principalTable: "model_headers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "model_workflows",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    model_header_id = table.Column<int>(type: "integer", nullable: false),
                    workflow_id = table.Column<int>(type: "integer", nullable: true),
                    workflow_permissions = table.Column<JsonDocument>(type: "json", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_model_workflows", x => x.id);
                    table.ForeignKey(
                        name: "fk_model_workflows_model_headers_model_header_id",
                        column: x => x.model_header_id,
                        principalSchema: "pan_gms",
                        principalTable: "model_headers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_model_workflows_workflow_definitions_workflow_id",
                        column: x => x.workflow_id,
                        principalSchema: "pan_gms",
                        principalTable: "workflow_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_amendment_data_tenant_id",
                schema: "pan_gms",
                table: "amendment_data",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_amendments_application_id",
                schema: "pan_gms",
                table: "amendments",
                column: "application_id");

            migrationBuilder.CreateIndex(
                name: "ix_amendments_tenant_id",
                schema: "pan_gms",
                table: "amendments",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_applicationdata_tenant_id",
                schema: "pan_gms",
                table: "applicationdata",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_applications_call_id",
                schema: "pan_gms",
                table: "applications",
                column: "call_id");

            migrationBuilder.CreateIndex(
                name: "ix_applications_currency_id",
                schema: "pan_gms",
                table: "applications",
                column: "currency_id");

            migrationBuilder.CreateIndex(
                name: "ix_applications_opportunity_id",
                schema: "pan_gms",
                table: "applications",
                column: "opportunity_id");

            migrationBuilder.CreateIndex(
                name: "ix_applications_tenant_id",
                schema: "pan_gms",
                table: "applications",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_units_obs_setting_id",
                schema: "pan_gms",
                table: "business_units",
                column: "obs_setting_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_units_tenant_id",
                schema: "pan_gms",
                table: "business_units",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_for_grants_tenant_id",
                schema: "pan_gms",
                table: "call_for_grants",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_program_stages_call_id",
                schema: "pan_gms",
                table: "call_program_stages",
                column: "call_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_program_stages_form_template_id",
                schema: "pan_gms",
                table: "call_program_stages",
                column: "form_template_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_program_stages_program_stage_id",
                schema: "pan_gms",
                table: "call_program_stages",
                column: "program_stage_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_program_stages_tenant_id",
                schema: "pan_gms",
                table: "call_program_stages",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_call_program_stages_workflow_id",
                schema: "pan_gms",
                table: "call_program_stages",
                column: "workflow_id");

            migrationBuilder.CreateIndex(
                name: "ix_categories_category_type_id",
                schema: "pan_gms",
                table: "categories",
                column: "category_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_categories_tenant_id",
                schema: "pan_gms",
                table: "categories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_category_options_category_id",
                schema: "pan_gms",
                table: "category_options",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_category_options_parent_option_id",
                schema: "pan_gms",
                table: "category_options",
                column: "parent_option_id");

            migrationBuilder.CreateIndex(
                name: "ix_category_options_tenant_id",
                schema: "pan_gms",
                table: "category_options",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_closure_application_id",
                schema: "pan_gms",
                table: "closure",
                column: "application_id");

            migrationBuilder.CreateIndex(
                name: "ix_closure_tenant_id",
                schema: "pan_gms",
                table: "closure",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_closure_data_tenant_id",
                schema: "pan_gms",
                table: "closure_data",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_concepts_tenant_id",
                schema: "pan_gms",
                table: "concepts",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_countries_tenant_id",
                schema: "pan_gms",
                table: "countries",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_country_id",
                schema: "pan_gms",
                table: "country_details",
                column: "country_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_fcs_id",
                schema: "pan_gms",
                table: "country_details",
                column: "fcs_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_fiscal_year_id",
                schema: "pan_gms",
                table: "country_details",
                column: "fiscal_year_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_income_group_id",
                schema: "pan_gms",
                table: "country_details",
                column: "income_group_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_lending_category_id",
                schema: "pan_gms",
                table: "country_details",
                column: "lending_category_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_region_id",
                schema: "pan_gms",
                table: "country_details",
                column: "region_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_sids_id",
                schema: "pan_gms",
                table: "country_details",
                column: "sids_id");

            migrationBuilder.CreateIndex(
                name: "ix_country_details_tenant_id",
                schema: "pan_gms",
                table: "country_details",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_currencies_code",
                schema: "pan_gms",
                table: "currencies",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_currencies_tenant_id",
                schema: "pan_gms",
                table: "currencies",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_data_elements_concept_id",
                schema: "pan_gms",
                table: "data_elements",
                column: "concept_id");

            migrationBuilder.CreateIndex(
                name: "ix_data_elements_data_type_id",
                schema: "pan_gms",
                table: "data_elements",
                column: "data_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_data_elements_tenant_id",
                schema: "pan_gms",
                table: "data_elements",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_document_sequence_info_tenant_id",
                schema: "pan_gms",
                table: "document_sequence_info",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_document_store_tenant_id",
                schema: "pan_gms",
                table: "document_store",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_document_store_details_document_store_id",
                schema: "pan_gms",
                table: "document_store_details",
                column: "document_store_id");

            migrationBuilder.CreateIndex(
                name: "ix_document_store_details_tenant_id",
                schema: "pan_gms",
                table: "document_store_details",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_events_event_type_id",
                schema: "pan_gms",
                table: "events",
                column: "event_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_events_tenant_id",
                schema: "pan_gms",
                table: "events",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_fiscal_years_tenant_id",
                schema: "pan_gms",
                table: "fiscal_years",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_data_framework_id",
                schema: "pan_gms",
                table: "framework_data",
                column: "framework_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_data_tenant_id",
                schema: "pan_gms",
                table: "framework_data",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_hierarchy_concept_id",
                schema: "pan_gms",
                table: "framework_hierarchy",
                column: "concept_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_hierarchy_framework_id",
                schema: "pan_gms",
                table: "framework_hierarchy",
                column: "framework_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_hierarchy_parent_concept_id",
                schema: "pan_gms",
                table: "framework_hierarchy",
                column: "parent_concept_id");

            migrationBuilder.CreateIndex(
                name: "ix_framework_hierarchy_tenant_id",
                schema: "pan_gms",
                table: "framework_hierarchy",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_frameworks_framework_type_id",
                schema: "pan_gms",
                table: "frameworks",
                column: "framework_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_frameworks_tenant_id",
                schema: "pan_gms",
                table: "frameworks",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_grant_types_tenant_id",
                schema: "pan_gms",
                table: "grant_types",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_collection_agent_type_id",
                schema: "pan_gms",
                table: "indicators",
                column: "collection_agent_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_collection_method_id",
                schema: "pan_gms",
                table: "indicators",
                column: "collection_method_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_data_type_id",
                schema: "pan_gms",
                table: "indicators",
                column: "data_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_format_id",
                schema: "pan_gms",
                table: "indicators",
                column: "format_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_indicator_type_id",
                schema: "pan_gms",
                table: "indicators",
                column: "indicator_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_measurement_type_id",
                schema: "pan_gms",
                table: "indicators",
                column: "measurement_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_parent_indicator_id",
                schema: "pan_gms",
                table: "indicators",
                column: "parent_indicator_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_reporting_type_id",
                schema: "pan_gms",
                table: "indicators",
                column: "reporting_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_indicators_tenant_id",
                schema: "pan_gms",
                table: "indicators",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_local_data_elements_concept_id",
                schema: "pan_gms",
                table: "local_data_elements",
                column: "concept_id");

            migrationBuilder.CreateIndex(
                name: "ix_local_data_elements_data_type_id",
                schema: "pan_gms",
                table: "local_data_elements",
                column: "data_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_local_data_elements_program_id",
                schema: "pan_gms",
                table: "local_data_elements",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_local_data_elements_tenant_id",
                schema: "pan_gms",
                table: "local_data_elements",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_look_up_info_look_up_type_id",
                schema: "pan_gms",
                table: "look_up_info",
                column: "look_up_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_look_up_info_tenant_id",
                schema: "pan_gms",
                table: "look_up_info",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_look_up_types_tenant_id",
                schema: "pan_gms",
                table: "look_up_types",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_menu_config_module_id",
                schema: "pan_gms",
                table: "menu_config",
                column: "module_id");

            migrationBuilder.CreateIndex(
                name: "ix_menu_config_parent_menu_id",
                schema: "pan_gms",
                table: "menu_config",
                column: "parent_menu_id");

            migrationBuilder.CreateIndex(
                name: "ix_menu_config_tenant_id",
                schema: "pan_gms",
                table: "menu_config",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_menu_details_menu_config_id",
                schema: "pan_gms",
                table: "menu_details",
                column: "menu_config_id");

            migrationBuilder.CreateIndex(
                name: "ix_menu_details_tenant_id",
                schema: "pan_gms",
                table: "menu_details",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_headers_entity_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "entity_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_headers_schema_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "schema_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_headers_tenant_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_schemas_tenant_id",
                schema: "pan_gms",
                table: "model_schemas",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_templates_model_header_id",
                schema: "pan_gms",
                table: "model_templates",
                column: "model_header_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_templates_tenant_id",
                schema: "pan_gms",
                table: "model_templates",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_workflows_model_header_id",
                schema: "pan_gms",
                table: "model_workflows",
                column: "model_header_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_workflows_tenant_id",
                schema: "pan_gms",
                table: "model_workflows",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_workflows_workflow_id",
                schema: "pan_gms",
                table: "model_workflows",
                column: "workflow_id");

            migrationBuilder.CreateIndex(
                name: "ix_module_groups_code",
                schema: "pan_gms",
                table: "module_groups",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_module_groups_tenant_id",
                schema: "pan_gms",
                table: "module_groups",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_modules_code",
                schema: "pan_gms",
                table: "modules",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_modules_module_group_id",
                schema: "pan_gms",
                table: "modules",
                column: "module_group_id");

            migrationBuilder.CreateIndex(
                name: "ix_modules_tenant_id",
                schema: "pan_gms",
                table: "modules",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_obs_settings_obs_type_id",
                schema: "pan_gms",
                table: "obs_settings",
                column: "obs_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_obs_settings_parent_obs_type_id",
                schema: "pan_gms",
                table: "obs_settings",
                column: "parent_obs_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_obs_settings_tenant_id",
                schema: "pan_gms",
                table: "obs_settings",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_opportunities_call_for_grants_id",
                schema: "pan_gms",
                table: "opportunities",
                column: "call_for_grants_id");

            migrationBuilder.CreateIndex(
                name: "ix_opportunities_tenant_id",
                schema: "pan_gms",
                table: "opportunities",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_opportunitydata_tenant_id",
                schema: "pan_gms",
                table: "opportunitydata",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_password_histories_tenant_id",
                schema: "pan_gms",
                table: "password_histories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_password_histories_user_id",
                schema: "pan_gms",
                table: "password_histories",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_performance_report_data_tenant_id",
                schema: "pan_gms",
                table: "performance_report_data",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_performance_reports_application_id",
                schema: "pan_gms",
                table: "performance_reports",
                column: "application_id");

            migrationBuilder.CreateIndex(
                name: "ix_performance_reports_tenant_id",
                schema: "pan_gms",
                table: "performance_reports",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_categories_category_id",
                schema: "pan_gms",
                table: "program_categories",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_categories_program_id",
                schema: "pan_gms",
                table: "program_categories",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_categories_tenant_id",
                schema: "pan_gms",
                table: "program_categories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_category_options_category_option_id",
                schema: "pan_gms",
                table: "program_category_options",
                column: "category_option_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_category_options_program_id",
                schema: "pan_gms",
                table: "program_category_options",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_category_options_tenant_id",
                schema: "pan_gms",
                table: "program_category_options",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_concepts_concept_id",
                schema: "pan_gms",
                table: "program_concepts",
                column: "concept_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_concepts_program_id",
                schema: "pan_gms",
                table: "program_concepts",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_concepts_tenant_id",
                schema: "pan_gms",
                table: "program_concepts",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_data_elements_data_element_id",
                schema: "pan_gms",
                table: "program_data_elements",
                column: "data_element_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_data_elements_program_id",
                schema: "pan_gms",
                table: "program_data_elements",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_data_elements_tenant_id",
                schema: "pan_gms",
                table: "program_data_elements",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_category_id",
                schema: "pan_gms",
                table: "program_hierarchy",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_parent_id",
                schema: "pan_gms",
                table: "program_hierarchy",
                column: "parent_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_program_id",
                schema: "pan_gms",
                table: "program_hierarchy",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_tenant_id",
                schema: "pan_gms",
                table: "program_hierarchy",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_categories_program_id",
                schema: "pan_gms",
                table: "program_hierarchy_categories",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_categories_tenant_id",
                schema: "pan_gms",
                table: "program_hierarchy_categories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_levels_parent_level_id",
                schema: "pan_gms",
                table: "program_hierarchy_levels",
                column: "parent_level_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_levels_program_hierarchy_element_id",
                schema: "pan_gms",
                table: "program_hierarchy_levels",
                column: "program_hierarchy_element_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_levels_program_id",
                schema: "pan_gms",
                table: "program_hierarchy_levels",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_hierarchy_levels_tenant_id",
                schema: "pan_gms",
                table: "program_hierarchy_levels",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_stage_grant_stage_id",
                schema: "pan_gms",
                table: "program_stage",
                column: "grant_stage_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_stage_program_id",
                schema: "pan_gms",
                table: "program_stage",
                column: "program_id");

            migrationBuilder.CreateIndex(
                name: "ix_program_stage_tenant_id",
                schema: "pan_gms",
                table: "program_stage",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_programs_code",
                schema: "pan_gms",
                table: "programs",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_programs_tenant_id",
                schema: "pan_gms",
                table: "programs",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_review_data_tenant_id",
                schema: "pan_gms",
                table: "review_data",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_reviews_application_id",
                schema: "pan_gms",
                table: "reviews",
                column: "application_id");

            migrationBuilder.CreateIndex(
                name: "ix_reviews_reviewer_id",
                schema: "pan_gms",
                table: "reviews",
                column: "reviewer_id");

            migrationBuilder.CreateIndex(
                name: "ix_reviews_tenant_id",
                schema: "pan_gms",
                table: "reviews",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_info_tenant_id",
                schema: "pan_gms",
                table: "role_info",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_mappings_menu_id",
                schema: "pan_gms",
                table: "role_menu_mappings",
                column: "menu_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_mappings_role_menu_master_id",
                schema: "pan_gms",
                table: "role_menu_mappings",
                column: "role_menu_master_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_mappings_tenant_id",
                schema: "pan_gms",
                table: "role_menu_mappings",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_masters_module_id",
                schema: "pan_gms",
                table: "role_menu_masters",
                column: "module_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_masters_role_id",
                schema: "pan_gms",
                table: "role_menu_masters",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_menu_masters_tenant_id",
                schema: "pan_gms",
                table: "role_menu_masters",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_permission_mappings_role_id",
                schema: "pan_gms",
                table: "role_permission_mappings",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_role_permission_mappings_tenant_id",
                schema: "pan_gms",
                table: "role_permission_mappings",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_sequence_info_table_name",
                schema: "pan_gms",
                table: "sequence_info",
                column: "table_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_sequence_info_tenant_id",
                schema: "pan_gms",
                table: "sequence_info",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_settings_data_type_id",
                schema: "pan_gms",
                table: "settings",
                column: "data_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_settings_module_id",
                schema: "pan_gms",
                table: "settings",
                column: "module_id");

            migrationBuilder.CreateIndex(
                name: "ix_settings_tenant_id",
                schema: "pan_gms",
                table: "settings",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_standardschemas_menu_id",
                schema: "pan_gms",
                table: "standardschemas",
                column: "menu_id");

            migrationBuilder.CreateIndex(
                name: "ix_standardschemas_tenant_id",
                schema: "pan_gms",
                table: "standardschemas",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_group_mapping_tenant_id",
                schema: "pan_gms",
                table: "user_group_mapping",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_group_mapping_user_groups_id",
                schema: "pan_gms",
                table: "user_group_mapping",
                column: "user_groups_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_groups_tenant_id",
                schema: "pan_gms",
                table: "user_groups",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                schema: "pan_gms",
                table: "user_info",
                column: "normalized_email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_user_info_role_id",
                schema: "pan_gms",
                table: "user_info",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_info_tenant_id",
                schema: "pan_gms",
                table: "user_info",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "LoginNameIndex",
                schema: "pan_gms",
                table: "user_info",
                column: "normalized_login_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_user_invitations_tenant_id",
                schema: "pan_gms",
                table: "user_invitations",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_invitations_user_id",
                schema: "pan_gms",
                table: "user_invitations",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_permissions_role_id",
                schema: "pan_gms",
                table: "user_permissions",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_user_permissions_tenant_id",
                schema: "pan_gms",
                table: "user_permissions",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_definitions_tenant_id",
                schema: "pan_gms",
                table: "workflow_definitions",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_histories_tenant_id",
                schema: "pan_gms",
                table: "workflow_histories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_instances_tenant_id",
                schema: "pan_gms",
                table: "workflow_instances",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_instances_workflow_definition_id",
                schema: "pan_gms",
                table: "workflow_instances",
                column: "workflow_definition_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_permissions_role_id",
                schema: "pan_gms",
                table: "workflow_permissions",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_permissions_tenant_id",
                schema: "pan_gms",
                table: "workflow_permissions",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_permissions_workflow_definition_id",
                schema: "pan_gms",
                table: "workflow_permissions",
                column: "workflow_definition_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_return_logs_tenant_id",
                schema: "pan_gms",
                table: "workflow_return_logs",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_steps_tenant_id",
                schema: "pan_gms",
                table: "workflow_steps",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_workflow_steps_workflow_definition_id",
                schema: "pan_gms",
                table: "workflow_steps",
                column: "workflow_definition_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "amendment_data",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "amendments",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "applicationdata",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "business_units",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "call_program_stages",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "closure",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "closure_data",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "country_details",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "document_sequence_info",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "document_store_details",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "events",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "framework_data",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "framework_hierarchy",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "grant_types",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "indicators",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "local_data_elements",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "menu_details",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "model_templates",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "model_workflows",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "opportunitydata",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "password_histories",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "performance_report_data",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "performance_reports",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_categories",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_category_options",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_concepts",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_data_elements",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_hierarchy",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_hierarchy_levels",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "review_data",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "reviews",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "role_menu_mappings",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "role_permission_mappings",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "sequence_info",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "settings",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "standardschemas",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "user_group_mapping",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "user_invitations",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "user_permissions",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_histories",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_instances",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_permissions",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_return_logs",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_steps",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "obs_settings",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "countries",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "fiscal_years",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "document_store",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "frameworks",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "model_headers",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "category_options",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "data_elements",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_hierarchy_categories",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "applications",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "role_menu_masters",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "menu_config",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "user_groups",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "user_info",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "workflow_definitions",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "model_schemas",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "program_stage",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "categories",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "concepts",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "currencies",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "opportunities",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "modules",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "role_info",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "programs",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "look_up_info",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "call_for_grants",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "module_groups",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "look_up_types",
                schema: "pan_gms");
        }
    }
}
