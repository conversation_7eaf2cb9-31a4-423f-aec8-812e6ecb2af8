
namespace PanApps.Unaids.Domain.System
{
    public class UserPermission : BaseEntity
    {
        public int UserId { get; set; }
        public int BusinessUnitId { get; set; }
        public int? AppId { get; set; }
        public int RoleId { get; set; }
        public virtual Role? Role { get; set; }
        public int? ModuleGroupId { get; set; }
        public int ModuleId { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public short? IsSystemDefined { get; set; }
        public string BusinessUnitValue { get; set; } = string.Empty;
        public bool UserSameLevelFlag { get; set; }
        public bool PermissionFlag { get; set; }
    }
}
