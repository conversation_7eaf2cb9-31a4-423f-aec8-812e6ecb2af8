﻿using Microsoft.AspNetCore.Authorization;
using PanApps.Core.Shared;
using System.Text.Json;

namespace PanApps.Authorization.Shared
{
    public class PermissionAuthorizationRequirement : AuthorizationHandler<PermissionAuthorizationRequirement>, IAuthorizationRequirement
    {
        public IEnumerable<string> Permissions { get; }

        public PermissionAuthorizationRequirement(IEnumerable<string> permissions)
        {
            Permissions = permissions ?? throw new ArgumentNullException(nameof(permissions));

        }

        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionAuthorizationRequirement requirement)
        {
            if (context.User == null || requirement.Permissions == null || !requirement.Permissions.Any())
                return Task.CompletedTask;

            var claims = context.User.Claims;

            var userRoles = JsonSerializer.Deserialize<int[]>(claims.Where(x => x.Type == CustomClaimTypes.Roles).Select(x => x.Value).FirstOrDefault() ?? "[]");

            if (userRoles != null && userRoles.Contains(1)) // sysadmin - No permission checking
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            var permissions = claims.Where(x => x.Type == CustomClaimTypes.Permissions)
                .Select(x => JsonSerializer.Deserialize<Dictionary<string, string>>(x.Value))
                .FirstOrDefault() ?? new Dictionary<string, string>();

            List<string> accessRights = new List<string>();

            foreach (var key in permissions.Keys)
            {
                if (permissions[key] != null)
                    accessRights.Add($"{key}.View");

                if (permissions[key].Contains("A"))
                    accessRights.Add($"{key}.Add");

                if (permissions[key].Contains("E"))
                    accessRights.Add($"{key}.Edit");

                if (permissions[key].Contains("D"))
                    accessRights.Add($"{key}.Delete");
            }

            var hasPermission = requirement.Permissions
                .Any(permission => accessRights.Contains(permission));

            if (!hasPermission) return Task.CompletedTask;

            context.Succeed(requirement);

            return Task.CompletedTask;
        }

    }
}
