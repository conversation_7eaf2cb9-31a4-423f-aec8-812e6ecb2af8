﻿namespace PanApps.Core.DocumentRepository
{
    public class DocumentRequestDto
    {
        public int Id { get; set; }
        public int DocumentStoreId { get; set; }
        public string? ReferenceNo { get; set; }
        public int? TransactionId { get; set; }
        public int? ServiceId { get; set; }
        public int? DocumentTagId { get; set; }
        public int? ModuleId { get; set; }
        public int? BusinessUnitId { get; set; }
        public int? DocCategoryId { get; set; }
        public string? UploadedName { get; set; }
        public string? Folder { get; set; }
        public string? UploadedPath { get; set; }
        public string? Description { get; set; }
        public DateTime? UploadedDate { get; set; }
        public string? FileName { get; set; }
        public int? FileSize { get; set; }
        public int? UserId { get; set; }
        public string? Remarks { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
