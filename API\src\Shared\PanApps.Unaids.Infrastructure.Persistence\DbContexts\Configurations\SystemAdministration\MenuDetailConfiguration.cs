using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class MenuDetailConfiguration : IEntityTypeConfiguration<MenuDetail>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<MenuDetail> builder)
        {
            builder.ToTable("menu_details");

            builder.HasKey(e => e.Id);

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.MenuConfigId).IsRequired();

            builder.HasOne(e => e.MenuConfig)
                   .WithMany()
                   .HasForeignKey(e => e.MenuConfigId)
                   .OnDelete(DeleteBehavior.Restrict);

            builder.Property(e => e.MenuAdd).IsRequired(false);
            builder.Property(e => e.MenuModify).IsRequired(false);
            builder.Property(e => e.Menucancel).IsRequired(false);
            builder.Property(e => e.MenuView).IsRequired(false);
            builder.Property(e => e.MenuPrint).IsRequired(false);
            builder.Property(e => e.MenuRePrint).IsRequired(false);
            builder.Property(e => e.MenuDelete).IsRequired();
            builder.Property(e => e.MenuProcess).IsRequired(false);
            builder.Property(e => e.MenuApprove).IsRequired();
            builder.Property(e => e.MenuPreDatedEntry).IsRequired();
            builder.Property(e => e.MenuImport).IsRequired();
            builder.Property(e => e.MenuExport).IsRequired();
            builder.Property(e => e.MenuValidation).IsRequired();
            builder.Property(e => e.MenuComments).IsRequired(false);
            builder.Property(e => e.MenuCorrect).IsRequired(false);
            builder.Property(e => e.MenuBulkImport).IsRequired(false);
            builder.Property(e => e.MenuExportRecord).IsRequired(false);
            builder.Property(e => e.MenuJSONEdit).IsRequired(false);
            builder.Property(e => e.MenuRestrictedView).IsRequired(false);
            builder.Property(e => e.MenuSpecial1).IsRequired(false);
            builder.Property(e => e.MenuSpecial2).IsRequired(false);
            builder.Property(e => e.MenuSpecial3).IsRequired(false);
            builder.Property(e => e.MenuSpecial4).IsRequired(false);
            builder.Property(e => e.MenuSpecial5).IsRequired(false);
            builder.Property(e => e.MenuToolTip).IsRequired(false);

            builder.Property(e => e.IsSystemDefined).IsRequired(false);
            builder.Property(e => e.Active).IsRequired(false);
            builder.Property(e => e.ModelDefinitionId).IsRequired(false);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new MenuDetailConfiguration());
        }
    }
}
