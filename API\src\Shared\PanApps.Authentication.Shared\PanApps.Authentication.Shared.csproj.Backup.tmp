﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PanApps.Core.Shared\PanApps.Core.Shared.csproj" />
    <ProjectReference Include="..\PanApps.Unaids.Infrastructure.Persistence\PanApps.Unaids.Infrastructure.Persistence.csproj" />
  </ItemGroup>
</Project>
