
using AutoMapper;
using PanApps.Core.Base.Contracts;
using PanApps.Unaids.Domain;

namespace PanApps.Core.Base
{
    public abstract class BaseAPIService<TEntity, TContext, TDTO, TRepository> : IBaseAPIService<TDTO> 
    where TEntity: BaseEntity
    where TContext: class
    where TDTO: class
    where TRepository: class, IBaseRepository<TEntity>
    {
        private readonly IUnitOfWork<TContext> unitOfWork;
        private readonly IMapper mapper;
        private readonly ISequenceRepository sequenceRepository;
        private readonly IUserContext userContext;

        public BaseAPIService(IUnitOfWork<TContext> _unitOfWork,
        IMapper _mapper,
        ISequenceRepository _sequenceRepository,
        IUserContext _userContext
        )
        {
            unitOfWork = _unitOfWork;
            mapper = _mapper;
            sequenceRepository = _sequenceRepository;
            userContext = _userContext;
        }

        /// <summary>
        /// Get all records
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public virtual async Task<List<TDTO>> GetAll(Filter filter)
        {
            var records = await unitOfWork.Repository<TRepository>().GetAllAsync();
            return mapper.Map<List<TDTO>>(records);
        }

        /// <summary>
        /// Get the record by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual async Task<TDTO> GetById(int id)
        {
            var record = await unitOfWork.Repository<TRepository>().GetByIdAsync(id);
            return mapper.Map<TDTO>(record);
        }

        /// <summary>
        /// Create the object
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public virtual async Task<TDTO> Create(TDTO dto)
        {
            if(dto == null)
                throw new ArgumentNullException(nameof(dto));

            var repo = unitOfWork.Repository<TRepository>();

            var objRec = mapper.Map<TEntity>(dto);
            objRec.Id = await sequenceRepository.GetSequenceNumber(repo.GetTableName());

            await repo.AddAsync(objRec);
            await unitOfWork.SaveChangesAsync();

            return mapper.Map<TDTO>(objRec);
        }

        /// <summary>
        /// update the record
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public virtual async Task<TDTO> Update(TDTO dto)
        {
            if(dto == null)
                throw new ArgumentNullException(nameof(dto));

            var objRec = mapper.Map<TEntity>(dto);
            await unitOfWork.Repository<TRepository>().UpdateAsync(objRec);
            await unitOfWork.SaveChangesAsync();

            return mapper.Map<TDTO>(objRec);
        }

        /// <summary>
        /// delete the record
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual async Task Delete(int id)
        {
            var repo = unitOfWork.Repository<TRepository>();
            await repo.DeleteAsync(id);

            await unitOfWork.SaveChangesAsync();
        }



        /// <summary>
        /// Get the user rights of the page
        /// </summary>
        /// <param name="subject"></param>
        /// <returns></returns>
        public string AccessRights(string subject)
        {
            return userContext.AccessRights(subject);
        }
    }
}