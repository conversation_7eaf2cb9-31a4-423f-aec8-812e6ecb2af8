﻿using System.Text.Json;
using System.Text.RegularExpressions;

namespace PanApps.Core.DataAccess.Dynamic
{
    public static class QueryUtils
    {
        public static string ToSnakeCase(this string input)
        {
            if (string.IsNullOrEmpty(input)) return input; 

            var startUnderscores = Regex.Match(input, @"^_+");
            return startUnderscores + Regex.Replace(input, @"([a-z0-9])([A-Z])", "$1_$2").ToLower();
        }

         public static string ToPascalCase(this string input)
        {
            if (string.IsNullOrEmpty(input)) return input;          
            return string.Concat(input.Split('_').Select(word => char.ToUpper(word[0]) + word[1..]));
        }

        public static bool Contains(this List<EntityProperties> props, string field)
        { return props.Any(x => x.Name == field); }

        public static object ValidateSupportedType(this object value, string errorContext)
        {
            return value switch
            {
                int or bool or decimal or double or short or Guid or DateTime or JsonDocument or string => value,
                _ => throw new ApplicationException($"Unsupported type {value?.GetType().Name} in {errorContext}")
            };
        }

        public static void ValidateField(this string field)
        {
            // Field is not null or empty
            if (string.IsNullOrWhiteSpace(field))
                throw new ApplicationException("Field cannot be null or empty.");

            // Only allow alphanumeric characters and underscores
            if (!Regex.IsMatch(field, @"^[a-zA-Z0-9_]+$"))
                throw new ApplicationException($"Invalid Field: {field}. Only letters, numbers, and underscores are allowed.");

            var sqlKeywords = new HashSet<string> {
                "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "ALTER", "CREATE", "EXEC",
                "UNION", "ALL", "FROM", "WHERE", "AND", "OR", "HAVING", "TABLE", "COLUMN"
            };

            // field that might be SQL keywords 
            if (sqlKeywords.Contains(field.ToUpper()))
                throw new ApplicationException($"Invalid Field: {field}. Reserved SQL keywords are not allowed.");
        }

        public static string FormatFieldPath(this string field)
        {
            var jsonPathParts = field.Split('.');
            string sqlField = jsonPathParts[0].ToSnakeCase();

            for (int i = 1; i < jsonPathParts.Length; i++)
            {
                jsonPathParts[i].ValidateField();
                sqlField += i == jsonPathParts.Length - 1 ? $"->>'{jsonPathParts[i]}'" : $"->'{jsonPathParts[i]}'";
            }

            return sqlField;
        }
    }
}
