﻿using System.Security.Cryptography;
using System.Text;

namespace PanApps.Core.Shared
{
    public static class PasswordUtility
    {
        private static readonly char[] DefaultPasswordChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()".ToCharArray();

        public static string HashPassword(string password, byte[] salt, int iterations = 100000)
        {
            byte[] hash = Rfc2898DeriveBytes.Pbkdf2(
                password: password,
                salt: salt,
                iterations: iterations,
                hashAlgorithm: HashAlgorithmName.SHA256,
                outputLength: 32 // 256 bits
            );

            return Convert.ToBase64String(salt) + ":" + Convert.ToBase64String(hash);
        }

        public static bool VerifyPassword(string password, string storedHash, int iterations = 100000)
        {
            var parts = storedHash.Split(':');
            if (parts.Length != 2)
                throw new FormatException("Invalid stored hash format.");

            var salt = Convert.FromBase64String(parts[0]);
            var hashOfInput = HashPassword(password, salt, iterations);
            return StringComparer.OrdinalIgnoreCase.Compare(hashOfInput, storedHash) == 0;
        }

        public static string GeneratePassword(int length = 12, bool includeNumbers = true, bool includeSymbols = true, bool includeUppercase = true, bool includeLowercase = true)
        {
            if (length < 1)
                throw new ArgumentException("Password length must be greater than 0", nameof(length));

            var passwordChars = BuildPasswordChars(includeNumbers, includeSymbols, includeUppercase, includeLowercase);
            var password = new char[length];
            var data = new byte[length];

            RandomNumberGenerator.Fill(data);

            for (int i = 0; i < length; i++)
            {
                var index = data[i] % passwordChars.Length;
                password[i] = passwordChars[index];
            }

            return new string(password);
        }

        private static char[] BuildPasswordChars(bool includeNumbers, bool includeSymbols, bool includeUppercase, bool includeLowercase)
        {
            var passwordChars = new StringBuilder();

            if (includeLowercase)
                passwordChars.Append("abcdefghijklmnopqrstuvwxyz");
            if (includeUppercase)
                passwordChars.Append("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            if (includeNumbers)
                passwordChars.Append("1234567890");
            if (includeSymbols)
                passwordChars.Append("!@#$%^&*()");

            return passwordChars.ToString().ToCharArray();
        }

        public static byte[] GenerateSalt(int size = 32)
        {
            var salt = new byte[size];
            RandomNumberGenerator.Fill(salt);
            return salt;
        }
    }
}
