﻿using Microsoft.AspNetCore.Http;
using PanApps.Core.Base.Contracts;
using System.Security.Claims;
using PanApps.Core.Shared;
using System.Text.Json;

namespace PanApps.Core.Base
{
    public sealed class UserContext(IHttpContextAccessor httpContextAccessor): IUserContext
    {
        public int Id =>
            httpContextAccessor.HttpContext?.User.GetUserId()
            ?? throw new ApplicationException("User context is unavailable");

        public string Name => httpContextAccessor.HttpContext?.User.GetUserName()
            ?? throw new ApplicationException("User context is unavailable");

        public string Email => httpContextAccessor.HttpContext?.User.GetUserEmail()
            ?? throw new ApplicationException("User context is unavailable");

        public int[] Tenants => httpContextAccessor.HttpContext?.User.GetUserTenants()
            ?? throw new ApplicationException("User context is unavailable");

        public int[] Roles => httpContextAccessor.HttpContext?.User.GetUserRoles() 
            ?? throw new ApplicationException("User context is unavailable");

        public Dictionary<string, string> Permissions => httpContextAccessor.HttpContext?.User.GetPermissions()
            ?? throw new ApplicationException("User context is unavailable");

        public string AccessRights(string subject)
        {
            if (this.Roles.Contains(1)) return "AED";

            return this.Permissions.ContainsKey(subject) ? string.IsNullOrEmpty(this.Permissions[subject])? "V" : this.Permissions[subject] : "";
        } 

    }

    internal static class ClaimsPrincipalExtensions
    {
        public static int GetUserId(this ClaimsPrincipal? principal)
        {
            string? userId = principal?.FindFirstValue(ClaimTypes.NameIdentifier);

            return int.TryParse(userId, out int parsedUserId) ?
                parsedUserId :
                throw new ApplicationException("User id is unavailable");
        }

        public static string GetUserName(this ClaimsPrincipal? principal)
        {
            string? userName = principal?.FindFirstValue(ClaimTypes.GivenName);

            return userName ??
                throw new ApplicationException("User Name is unavailable");
        }

        public static string GetUserEmail(this ClaimsPrincipal? principal)
        {
            string? userEmail = principal?.FindFirstValue(ClaimTypes.Email);

            return userEmail ??
                throw new ApplicationException("User Email is unavailable");
        }

        public static int[] GetUserTenants(this ClaimsPrincipal? principal)
        {
            string? userTenants = principal?.FindFirstValue(CustomClaimTypes.TenantId);

            if (string.IsNullOrEmpty(userTenants))
            {
                throw new ApplicationException("User Tenents is unavailable");
            }

            return JsonSerializer.Deserialize<int[]>(userTenants);
        }

        public static int[] GetUserRoles(this ClaimsPrincipal? principal)
        {
            string? userRoles = principal?.FindFirstValue(CustomClaimTypes.Roles);

            if (string.IsNullOrEmpty(userRoles))
            {
                throw new ApplicationException("User Roles is unavailable");
            }

            return JsonSerializer.Deserialize<int[]>(userRoles);
        }

        public static Dictionary<string, string> GetPermissions(this ClaimsPrincipal? principal)
        {
            string? userPermissions = principal?.FindFirstValue(CustomClaimTypes.Permissions);

            if (string.IsNullOrEmpty(userPermissions))
            {
                throw new ApplicationException("User Permissions is unavailable");
            }

            return JsonSerializer.Deserialize<Dictionary<string, string>>(userPermissions);
        }
    }

}
