﻿using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base;
using PanApps.PanGMS.Infrastructure.Persistence;

namespace PanApps.Core.Taxonomy
{
    public class GenericDropdownRepository(ProgramAdminDbContext dbContext): IGenericDropdownRepository
    {
        public async Task<Dictionary<string, List<CommonLookUp>>> GetOptions(string[] lookupTypes, int tenantId)
        {
            var dicLookUp = new Dictionary<string, List<CommonLookUp>>();

            foreach (var lookupType in lookupTypes)
            {
                var arr = lookupType.Split('_');
                if(arr.Count() > 0)
                {
                    switch(arr[0].ToLower())
                    {
                        case "lookup":
                            dicLookUp[lookupType] = (arr.Length >= 1) ?await GetLookUps(arr[1]) : new List<CommonLookUp>();
                            break;
                        case "categories":
                            dicLookUp[lookupType] = (arr.Length >= 1) ? await GetCategoryOptions(arr[1]) : new List<CommonLookUp>();
                            break;
                        case "userlist":
                            dicLookUp[lookupType] = (arr.Length >= 1) ? await GetUsers(arr[1], tenantId) : new List<CommonLookUp>();
                            break;
                    }
                }                
            }

            return dicLookUp;
        }


        /// <summary>
        /// Get the list of users
        /// </summary>
        /// <param name="code"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetUsers(string code, int tenantId)
        {
            return await (from usr in dbContext.Users.AsNoTracking()
                          join perm in dbContext.UserPermissions.AsNoTracking()
                          on usr.Id equals perm.UserId
                          join role in dbContext.Roles.AsNoTracking()
                          on perm.RoleId equals role.Id
                          where perm.BusinessUnitId == tenantId
                          select new CommonLookUp()
                          {
                              Value = usr.Id,
                              Active = 1,
                              DisplayText = usr.UserName
                          }).Distinct().ToListAsync();
        }

        /// <summary>
        /// get lookups
        /// </summary>
        /// <param name="fieldName"></param>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetLookUps(string fieldName)
        {
            return await (from info in dbContext.LookUpInfo.AsNoTracking()
                          join lkp in dbContext.LookUpTypes.AsNoTracking()
                          on info.LookUpTypeId equals lkp.Id
                          where lkp.FieldName == fieldName
                          select new CommonLookUp()
                          {
                              Value = info.Id,
                              Active = 1,
                              DisplayText = info.Name,
                              DisplayCode = info.Code
                          }).ToListAsync();
        }

        /// <summary>
        /// get category options
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetCategoryOptions(string code)
        {
            return await (from cat in dbContext.Categories.AsNoTracking()
                          join option in dbContext.CategoryOptions.AsNoTracking()
                          on cat.Id equals option.CategoryId
                          where cat.Code == code
                          select new CommonLookUp()
                          {
                              Value = option.Id,
                              Active = 1,
                              DisplayText = option.Name,
                              DisplayCode = option.Code ?? ""
                          }).ToListAsync();
        }


    }
}
