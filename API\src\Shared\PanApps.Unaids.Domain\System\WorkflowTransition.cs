﻿using System.ComponentModel.DataAnnotations.Schema;

namespace PanApps.Unaids.Domain
{
    public class WorkflowTransition : BaseEntity
    {
        public int FromStepId { get; set; }
        public int ToStepId { get; set; }

        [Column(TypeName = "jsonb")]
        public TransitionProperty? Property { get; set; }
    }

    public class TransitionProperty
    {
        public List<TransitionPermission>? Permissions { get; set; } = new();
    }

    public class TransitionPermission
    {
        public string Type { get; set; } // User, Role, Group
        public List<int> ReferenceIds { get; set; }
    }
}
