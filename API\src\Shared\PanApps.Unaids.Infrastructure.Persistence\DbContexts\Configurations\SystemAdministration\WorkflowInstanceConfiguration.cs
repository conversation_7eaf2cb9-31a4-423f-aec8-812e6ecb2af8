﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class WorkflowInstanceConfiguration : IEntityTypeConfiguration<WorkflowInstance>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowInstance> builder)
        {
            builder.ToTable("workflow_instances");
            builder.HasKey(e => e.WorkflowInstanceId);
            builder.Property(e => e.WorkflowInstanceId).ValueGeneratedNever();

            builder.Property(e => e.WorkflowDefinitionId).IsRequired();
            builder
                .HasOne(e => e.WorkflowDefinition)
                .WithMany()
                .HasForeignKey(e => e.WorkflowDefinitionId)
                .HasPrincipalKey(e => e.Id);

            builder.Property(e => e.CurrentStatus).HasMaxLength(50);
            
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowInstanceConfiguration());
        }
    }
}
