﻿using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base;
using PanApps.PanGMS.Domain;
using PanApps.PanGMS.Infrastructure.Persistence;

namespace PanApps.Core.DocumentRepository
{
    public sealed class DocumentStoreDetailRepository(CommonDbContext dbContext): BaseRepository<DocumentStoreDetails>(dbContext), IDocumentStoreDetailRepository
    {
        public async Task<Boolean> AnyDocsAsync(int storeId)
        {
            return await dbContext.DocumentStoreDetails.AsNoTracking().AnyAsync(x => x.DocumentStoreId == storeId);
        }
    }
}
