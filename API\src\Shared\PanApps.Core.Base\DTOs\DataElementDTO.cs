﻿using PanApps.Unaids.Domain.Common;

namespace PanApps.Core.Base
{
    public class DataElementDTO
    {
        public int Id { get; set; }
        public int? ConceptId { get; set; }
        public string? Code { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public short? IsRequired { get; set; }
        public string? DataType { get; set; }
        public int? MaxLength { get; set; }
        public string? Format { get; set; }
        public string? DataGroupName { get; set; }
        public ModelNavigation? Navigation { get; set; }
        public List<CommonLookUp>? LookUps { get; set; }
        public List<DataElementLookUp>? Options { get; set; }
    }

    public class ModelNavigation
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public List<DataElementDTO>? DataElements { get; set; }
    }

    public class DataElementLookUp
    {
        public int Id { get; set; }
        public string DisplayText { get; set; }  = string.Empty;
    }
}
