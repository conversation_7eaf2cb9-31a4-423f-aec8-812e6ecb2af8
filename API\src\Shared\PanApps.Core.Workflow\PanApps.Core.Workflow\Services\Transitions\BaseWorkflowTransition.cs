﻿using Microsoft.Extensions.DependencyInjection;
using PanApps.Core.Base;
using PanApps.Unaids.Infrastructure.Persistence;
using System.Diagnostics;

namespace PanApps.Core.Workflow
{
    public class BaseWorkflowTransition (IUnitOfWork<SystemAdminDbContext> unitOfWork, IServiceProvider serviceProvider)
    {
        /// <summary>
        /// Validation on workflow step transition
        /// </summary>
        /// <param name="workflowExecutionContext"></param>
        /// <param name="nextStepId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        protected async Task<WorkflowExecutorResult> Validate(WorkflowExecutionContext workflowExecutionContext, int nextStepId, int userId)
        {

            var workflowInstance = workflowExecutionContext.WorkflowInstance;
            var stepTransitions = workflowExecutionContext.Workflow.StepHierarchy;
            int? currentStepId = workflowExecutionContext.WorkflowInstance.CurrentStepId;

            var workflowExecutorResult = new WorkflowExecutorResult();

            if (currentStepId != null &&
                !stepTransitions.ContainsKey(currentStepId ?? 0))
            {
                workflowExecutorResult.Errors.Add(new ExecutionError()
                {
                    WorkflowId = workflowInstance.WorkflowDefinitionId.ToString(),
                    Message = "The workflow step cannot be found or user does not have permission",
                    ErrorTime = DateTime.UtcNow
                });
            }
            else if (currentStepId != null)
            {
                var allowedSteps = stepTransitions[currentStepId ?? 0];
                if (!allowedSteps.Contains(nextStepId))
                {
                    workflowExecutorResult.Errors.Add(new ExecutionError()
                    {
                        WorkflowId = workflowInstance.WorkflowDefinitionId.ToString(),
                        Message = "The workflow step cannot be found or user does not have permission",
                        ErrorTime = DateTime.UtcNow
                    });
                }
            }

            return await Task.FromResult(workflowExecutorResult);
        }

        /// <summary>
        /// Derive the activity name from the from and to step
        /// </summary>
        /// <param name="workflow"></param>
        /// <param name="fromStepId"></param>
        /// <param name="nextStepId"></param>
        /// <returns></returns>
        protected async Task<string> DeriveActivityName(WorkflowPermissionMapping workflow, int fromStepId, int nextStepId)
        {
            var repo = unitOfWork.Repository<IWorkflowRepository>();
            var activity = await repo.GetWorkflowName(workflow.WorkflowDefinitionId);

            if (fromStepId > 0)
            {
                var fromStep = await repo.GetWorkflowStep(fromStepId);
                activity = $"{activity}{fromStep}";
            }

            if (nextStepId > 0)
            {
                var toStep = await repo.GetWorkflowStep(nextStepId);
                activity = $"{activity}To{toStep}";
            }

            activity = activity?.ConvertToActivityName() ?? "";

            return activity;
        }

        /// <summary>
        /// Get the pre and post activities assosiated with the step
        /// </summary>
        /// <param name="workflow"></param>
        /// <param name="nextStepId"></param>
        /// <returns></returns>
        protected async Task<IWorkflowActivity?> GetActivity(WorkflowPermissionMapping workflow, int currentStepId, int nextStepId, string activity)
        {
            // Get activity assigned to the transition
            bool triggerService = workflow.Transitions.FirstOrDefault(x => x.FromStepId == currentStepId && x.ToStepId == nextStepId)?.TriggerService ?? false;

            IWorkflowActivity activityInstance = null;

            if (triggerService)
            {
                // Get all services of IActivity fom DI container
                var services = serviceProvider.GetServices<IWorkflowActivity>();

                if (services == null)
                    throw new NotFoundException("No activity service was registered in DI container");

                // Get activity instance assigned to the transition
                activityInstance = services.FirstOrDefault(x => x.Type == activity);

                if (activityInstance == null)
                    throw new NotFoundException($"The activity named '{activity}' was not found");
            }

            return activityInstance;
        }
    }
}
