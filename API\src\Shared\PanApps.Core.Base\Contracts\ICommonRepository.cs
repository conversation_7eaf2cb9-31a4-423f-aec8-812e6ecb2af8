﻿namespace PanApps.Core.Base
{
    public interface ICommonRepository
    {
        Task<List<CommonLookUp>> GetUsers(string roleCode);
        Task<List<CommonLookUp>> GetCalls();
        Task<List<CommonLookUp>> GetTenants();
        Task<List<FormTemplateDTO>?> GetModelFormTemplate(int modelId);
        Task<int?> GetStageWorkflow(int callId, string stageCode);
        Task<string?> GetStageDocFormat(int callId, string stageCode);
        Task<List<CommonLookUp>> GetDynamicModels(string entity);
    }
}
