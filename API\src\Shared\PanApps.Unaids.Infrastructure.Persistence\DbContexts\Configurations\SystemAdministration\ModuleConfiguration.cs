﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class ModuleConfiguration : IEntityTypeConfiguration<Module>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Module> builder)
        {
            builder.ToTable("modules");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.HasIndex(e => e.Code).IsUnique();
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(10);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(50);
            builder.Property(e => e.Description).IsRequired(false).HasMaxLength(200);
            builder.Property(e => e.Color).IsRequired(false).HasMaxLength(50);
            builder.Property(e => e.Icon).IsRequired(false).HasMaxLength(50);
            builder.Property(e => e.DashboardURL).IsRequired(false).HasMaxLength(2000);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new ModuleConfiguration());
        }
    }
}
