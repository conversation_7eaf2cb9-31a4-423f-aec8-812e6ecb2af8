﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class WorkflowDefinitionConfiguration : IEntityTypeConfiguration<WorkflowDefinition>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowDefinition> builder)
        {
            builder.ToTable("workflow_definitions");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Description).IsRequired().HasMaxLength(200);
            builder.Property(w => w.Properties).HasColumnType("jsonb");
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowDefinitionConfiguration());
        }
    }
}
