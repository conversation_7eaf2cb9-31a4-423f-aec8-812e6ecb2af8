using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;
using PanApps.Unaids.Infrastructure.Persistence;

namespace PanApps.SystemAdministration.Infrastructure.Persistence
{
    public class RoleMenuMappingConfiguration : IEntityTypeConfiguration<RoleMenuMapping>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<RoleMenuMapping> builder)
        {
            // Set table name
            builder.ToTable("role_menu_mappings");

            // Configure primary key
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).UseSerialColumn();

            // Configure MenuId (foreign key to Menu)
            builder.Property(e => e.MenuId)
                .IsRequired(); // MenuId is required

            builder.HasOne(e => e.Menu)
                .WithMany()
                .HasForeignKey(e => e.MenuId)
                .OnDelete(DeleteBehavior.Restrict); // Optional: Configure delete behavior

            // Configure RoleMenuMasterId (foreign key to RoleMenuMaster)
            builder.Property(e => e.RoleMenuMasterId)
                .IsRequired(false); // Nullable field

            builder.HasOne(e => e.RoleMenuMaster)
                .WithMany(e => e.RoleMenuMapping)
                .HasForeignKey(e => e.RoleMenuMasterId)
                .OnDelete(DeleteBehavior.Cascade); // Optional: Set delete behavior

            // Configure optional permissions fields (e.g., MenuAdd, MenuModify, etc.)
            builder.Property(e => e.MenuAdd).IsRequired(false);
            builder.Property(e => e.MenuModify).IsRequired(false);
            builder.Property(e => e.MenuCancel).IsRequired(false);
            builder.Property(e => e.MenuView).IsRequired(false);
            builder.Property(e => e.MenuPrint).IsRequired(false);
            builder.Property(e => e.MenuRePrint).IsRequired(false);
            builder.Property(e => e.MenuDelete).IsRequired(false);
            builder.Property(e => e.MenuProcess).IsRequired(false);
            builder.Property(e => e.MenuApprove).IsRequired(false);
            builder.Property(e => e.MenuPreDatedEntry).IsRequired(false);
            builder.Property(e => e.MenuImport).IsRequired(false);
            builder.Property(e => e.MenuExport).IsRequired(false);
            builder.Property(e => e.MenuValidation).IsRequired(false);
            builder.Property(e => e.MenuCorrect).IsRequired(false);
            builder.Property(e => e.MenuBulkImport).IsRequired(false);
            builder.Property(e => e.MenuBulkUpdate).IsRequired(false);
            builder.Property(e => e.MenuBulkDelete).IsRequired(false);
            builder.Property(e => e.MenuExportRecord).IsRequired(false);
            builder.Property(e => e.RestrictedView).IsRequired(false);
            builder.Property(e => e.MenuJSONEdit).IsRequired(false);
            builder.Property(e => e.MenuSpecial1).IsRequired(false);
            builder.Property(e => e.MenuSpecial2).IsRequired(false);
            builder.Property(e => e.MenuSpecial3).IsRequired(false);
            builder.Property(e => e.MenuSpecial4).IsRequired(false);
            builder.Property(e => e.MenuSpecial5).IsRequired(false);

            // Configure IsSystemDefined
            builder.Property(e => e.IsSystemDefined)
                .IsRequired(false); 

            // Configure NotMapped properties
            builder.Ignore(e => e.MenuType);
            builder.Ignore(e => e.MenuName);
            builder.Ignore(e => e.MenuTypeName);
        }
        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RoleMenuMappingConfiguration());
        }
    }
}
