﻿using Microsoft.AspNetCore.Builder;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public static class MiddlewareExtensions
    {
        /// <summary>
        /// Adds a Multitenancy middleware to your web application pipeline to set current tenant according to request header.
        /// This middleware should be between UseStaticFiles and UseRouting.
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseMultitenancy(this IApplicationBuilder builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            //return builder.UseWhen(context => !context.Request.Path.StartsWithSegments("/api/application-settings"), appBuilder =>
            //{
            //    appBuilder.UseMiddleware<TenantResolutionMiddleware>();
            //});

            return builder.UseMiddleware<TenantResolutionMiddleware>();
        }
    }
}
