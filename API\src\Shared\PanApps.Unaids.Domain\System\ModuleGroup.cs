﻿namespace PanApps.Unaids.Domain.System
{
    public class ModuleGroup: BaseEntity
    {
        public string? Code { get; set; }
        public string Name { get; set; } = "";
        public string? Description { get; set; }
        public int? SortOrder { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? DashboardURL { get; set; }
        public string? TileMenuPos { get; set; }
        public ICollection<Module>? Module { get; set; }
    }
}
