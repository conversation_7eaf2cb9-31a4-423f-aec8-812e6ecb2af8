﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.TenantManagement;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class ProgramConfiguration : IEntityTypeConfiguration<Program>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Program> builder)
        {
            builder.ToTable("programs");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.HasIndex(e => e.Code).IsUnique();
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(10);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Description).IsRequired(false).HasMaxLength(200);
        }
        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new ProgramConfiguration());
        }
    }
}
