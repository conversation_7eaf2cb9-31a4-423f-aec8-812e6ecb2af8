﻿namespace PanApps.Core.Workflow
{
    public class WorkflowPermissionMapping
    {
        public int WorkflowDefinitionId { get; set; }
        public List<WorkflowTransition> Transitions { get; set; } = new List<WorkflowTransition>();
        public Dictionary<int, int[]> StepHierarchy { get; set; } = new Dictionary<int, int[]>();
    }

    public class WorkflowTransition
    {
        public int FromStepId { get; set; }
        public int ToStepId { get; set; }
        public bool? TriggerService { get; set; }
    }
}
