using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class BusinessUnitConfiguration : IEntityTypeConfiguration<BusinessUnit>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<BusinessUnit> builder)
        {
            builder.ToTable("business_units");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            
            builder.Property(e => e.Code).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Address).HasMaxLength(250);
            builder.Property(e => e.PinCode).HasMaxLength(20);
            builder.Property(e => e.Phone).HasMaxLength(20);
            builder.Property(e => e.Fax).HasMaxLength(20);
            builder.Property(e => e.Email).HasMaxLength(100);
            builder.Property(e => e.WebSite).HasMaxLength(100);
            builder.Property(e => e.Remarks).HasMaxLength(500);
            builder.Property(e => e.Hierarchy).HasMaxLength(500);
            builder.Property(e => e.GeoCoordinates).HasMaxLength(100);
            builder.Property(e => e.SettingDetail).HasMaxLength(500);
            builder.Property(e => e.Logo).HasMaxLength(200);
            
            builder.Property(e => e.Active).IsRequired();
            builder.Property(e => e.Level).IsRequired();
            builder.Property(e => e.UpdatedDate).IsRequired(false);
            
            builder
                .HasOne(e => e.OBSSetting)
                .WithMany()
                .HasForeignKey(e => e.OBSSettingId);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new BusinessUnitConfiguration());
        }
    }
}
