﻿using Dapper;
using System.Text;

namespace PanApps.Core.DataAccess.Dynamic.QueryBuilder
{
    public static class QueryBuilder
    {
        public static string BuildQuery(this DbEntity entity, Query objQuery, ref DynamicParameters parameters)
        {
            var q = new StringBuilder("");            

            q.BuildSelectQuery(entity, objQuery.SelectedProperties);
            q.BuildFromQuery(entity);

            if (objQuery.FilterRules != null && objQuery.FilterRules.Count > 0) 
                q.BuildFilterQuery(entity, objQuery.FilterRules, ref parameters);                      

            return q.ToString().TrimEnd();
        }

        public static void BuildSelectQuery(this StringBuilder q, DbEntity entity, string[]? props)
        {
            q.Append("SELECT ");

            var selectedProps = (props == null || props.Length == 0)
                ? entity.Properties.Select(p => $"{p.Name.ToSnakeCase()} AS \"{p.Name}\"")
                : props.Select(prop =>
                {
                    var baseProp = prop.Split(".")[0];
                    if (!entity.Properties.Any(p => p.Name == baseProp))
                        throw new ApplicationException($"Dynamic Query Exception: Invalid column {prop}");

                    return $"{prop.FormatFieldPath()} AS \"{prop.Split(".").Last()}\"";
                });

            q.Append(string.Join(", ", selectedProps));
        }

        public static void BuildFromQuery (this StringBuilder q, DbEntity entity)
        {
            if (string.IsNullOrEmpty(entity.Name))
                throw new ApplicationException("Dynamic Query Exception: Table is missing");

            q.Append($" FROM {entity.Schema}.{entity.Name} "); 
        }

    }
}
