using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class LookUpTypeConfiguration : IEntityTypeConfiguration<LookUpType>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<LookUpType> builder)
        {
            builder.ToTable("look_up_types");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.FieldName).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Description).IsRequired(false).HasMaxLength(200);
            builder.Property(e => e.IsSystemDefined).IsRequired(false);
            builder
                .HasMany(e => e.LookUpInfo)
                .WithOne(e => e.LookUpType)
                .HasForeignKey(e => e.LookUpTypeId)
                .HasPrincipalKey(e => e.Id);
        }
        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new LookUpTypeConfiguration());
        }
    }
}
