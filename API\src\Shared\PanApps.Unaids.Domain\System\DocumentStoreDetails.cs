﻿
namespace PanApps.Unaids.Domain
{
    public class DocumentStoreDetails: BaseEntity
    {
        public int DocumentStoreId { get; set; }
        public virtual DocumentStore? DocumentStore { get; set; }

        public string? Title { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string? UploadedName { get; set; }
        public string? UploadedPath { get; set; }
        public DateTime? UploadedDate { get; set; }
        public string? Remarks { get; set; }
        public int? FileSize { get; set; }
        public int? UserId { get; set; }

    }
}
