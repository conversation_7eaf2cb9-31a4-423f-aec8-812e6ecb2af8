using PanApps.Unaids.Domain;
using System.ComponentModel.DataAnnotations.Schema;

namespace PanApps.Model.Administration
{
    [Table(name: "UserGroupMapping")]
    public class UserGroupMapping : BaseEntity 
    {
        public int UserGroupsId { get; set; }
        [ForeignKey("UserGroupsId")]
        public virtual UserGroups? UserGroups { get; set; }
        public int UserId { get; set; }
        public short DefaultUserGroup { get; set; }
    }
}
