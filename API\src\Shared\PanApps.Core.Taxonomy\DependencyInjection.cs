﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PanApps.Core.Base;
using PanApps.Core.Taxonomy.Repositories;
using PanApps.Core.Taxonomy.Services;

namespace PanApps.Core.Taxonomy
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddTaxonomyServices(this IServiceCollection services, IConfiguration configuration)
            => services
                .AddScoped<IGenericDropdownRepository, GenericDropdownRepository>()
                .AddScoped<ITaxonomyRepository, TaxonomyRepository>()
                .AddScoped<ITaxonomyService, TaxonomyService>();
    }
}
