﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class PasswordHistoryConfiguration : IEntityTypeConfiguration<PasswordHistory>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<PasswordHistory> builder)
        {
            builder.ToTable("password_histories");

            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedOnAdd();
            builder.Property(c => c.UserId).IsRequired();
            builder.Property(c => c.PasswordHash).IsRequired().HasMaxLength(256);

            builder.HasOne(c => c.User)
                .WithMany(c => c.PasswordHistories)
                .HasForeignKey(e => e.UserId);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new PasswordHistoryConfiguration());
        }
    }
}
