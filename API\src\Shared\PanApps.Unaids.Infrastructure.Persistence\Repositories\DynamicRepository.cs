﻿using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base;

namespace PanApps.Core.DataAccess.Dynamic
{
    public class DynamicRepository<TEntity, TContext>(TContext dbContext): BaseRepository<TEntity>(dbContext),  IDynamicRepository<TEntity> where TEntity: class where TContext : DbContext
    {
        public IQueryable<TEntity> AsQueriable()
        {
            return _dbSet.AsQueryable();
        }
    }
}
