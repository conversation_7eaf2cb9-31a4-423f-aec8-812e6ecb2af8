﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class MenuConfiguration : IEntityTypeConfiguration<Menu>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Menu> builder)
        {
            builder.ToTable("menu_config");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Title).IsRequired().HasMaxLength(100);
            builder.Property(e => e.MenuType).IsRequired();
            builder.Property(e => e.URL).IsRequired(false).HasMaxLength(2000);
            builder.Property(e => e.Remarks).IsRequired(false).HasMaxLength(500);
            builder.Property(e => e.Path).IsRequired(false).HasMaxLength(2000);
            builder.Property(e => e.Icon).IsRequired(false).HasMaxLength(50);
            builder.Property(e => e.Color).IsRequired(false).HasMaxLength(50);
            builder.Property(e => e.ComponentName).IsRequired(false).HasMaxLength(100);
            builder.Property(e => e.ConfigurationSettings).IsRequired(false).HasColumnType("json");
            builder.Property(e => e.ControllerType).IsRequired(false).HasMaxLength(50);
            builder.Property(e => e.Area).IsRequired(false).HasMaxLength(100);
            builder.Property(e => e.Controller).IsRequired(false).HasMaxLength(100);
            builder.Property(e => e.Action).IsRequired(false).HasMaxLength(100);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new MenuConfiguration());
        }
    }
}
