﻿namespace PanApps.Core.Workflow
{
    public class Variables
    {
        public IDictionary<string, object> Data { get; }

        public Variables()
        {
            Data = new Dictionary<string, object>();
        }

        public Variables(IDictionary<string, object> data)
        {
            Data = data;
        }

        public object Get(string name) => Has(name) ? Data[name] : default;
        //public T Get<T>(string name) => !Has(name) ? default : Get(name).ConvertTo<T>();

        public Variables Set(string name, object value)
        {
            Data[name] = value;
            return this;
        }

        public Variables Remove(string name)
        {
            Data.Remove(name);
            return this;
        }

        public Variables RemoveAll()
        {
            Data.Clear();
            return this;
        }

        public bool Has(string name) => Data.ContainsKey(name);
    }
}
