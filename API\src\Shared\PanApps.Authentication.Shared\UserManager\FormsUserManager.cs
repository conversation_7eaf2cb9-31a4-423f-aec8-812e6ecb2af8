﻿
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;
using PanApps.Core.Shared;
using PanApps.Unaids.Domain.System;
using PanApps.Unaids.Infrastructure.Persistence;
using System.Text;
using System.Xml.Linq;
using static Org.BouncyCastle.Math.EC.ECCurve;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace PanApps.Authentication.Shared
{
    public class FormsUserManager (
        IUnitOfWork<CommonDbContext> _unitOfWork,
        IMapper _mapper,
        ISequenceRepository _sequenceRepository,
        ITenantProvider _tenantProvider,
        IUserContext _userContext,
        IHttpContextAccessor _httpContextAccessor,
        IEmailService _emailService,
        IConfiguration configuration
        ) : BaseUserManager(
            _unitOfWork,
            _mapper,
            _sequenceRepository,
            _tenantProvider,
            _userContext
        ), IUserManager
    {
        
        public override async Task Invite(User user)
        {
            string token = Convert.ToBase64String(Guid.NewGuid().ToByteArray());
            var invitationURL = $"{_httpContextAccessor.HttpContext.Request.Headers["Origin"]}/activation/{token}";

            var invitation = new UserInvitation()
            {
                InvitationURL = invitationURL,
                Token = token,
                Email = user.Email,
                UserId = user.Id,
                ExpireOn = DateTime.UtcNow.AddDays(1),
                CreatedAt = DateTime.UtcNow,
                CreatedById = _userContext.Id
            };

            await _unitOfWork.Repository<IUserRepository>().AddUserInvitation(invitation);

            // send email...
            var applicationName = configuration.GetSection("AppSettings:ApplicationName").Value ?? string.Empty;
            var subject = $"{applicationName} : User account activation details";

            var messageBuilder = new StringBuilder("<html><body>");

            messageBuilder.Append("<table style='font-family:Helvetica,Arial,sans-serif;border-collapse:collapse;width:100% !important;font-family:Helvetica,Arial,sans-serif;padding:0;' border='0' cellpadding='0' cellspacing='0' width='100%'>");
            messageBuilder.Append("<tr><td><div style='height:20px;line-height:20px;'>&nbsp;</div></td></tr>");
            messageBuilder.Append("<tr><td>Hi " + user.UserName.Trim() + ",</td></tr><tr><td><div style='height:20px;line-height:20px;'>&nbsp;</div></td></tr>");
            messageBuilder.Append("<tr><td>Your " + applicationName + " account has been successfully created on " + DateTime.Now.ToString("d MMM yyyy HH:mm:ss") + ".");
            messageBuilder.Append("<tr><td>Kindly click on the below link to activate the account.");
            messageBuilder.Append($"<tr><td><a href='{invitationURL}'>Activate</a></td></tr>");
            messageBuilder.Append("<tr><td>This mail is automatically generated. Please do not reply. If you have any queries, please contact the " + applicationName + " team.</td></tr><tr><td><div style='height:20px;line-height:20px;'>&nbsp;</div></td></tr>");
            messageBuilder.Append("</table>");
            messageBuilder.Append("</body></html>");

            await _emailService.SendMailAsync(
                user.Email,
                subject,
                messageBuilder.ToString(),
                true
            );
        }

    }
}
