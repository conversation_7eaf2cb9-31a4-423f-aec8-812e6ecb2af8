﻿using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using System;
using System.Linq.Expressions;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public static class GlobalQueryFilterExtensions
    {
        ///// <summary>
        ///// Applies the query filter globally to all entities derived from TBase
        ///// </summary>
        ///// <typeparam name="TBase"></typeparam>
        ///// <param name="modelBuilder"></param>
        ///// <param name="filter"></param>
        //public static void ApplyGlobalFilters<TBase>(
        //    this ModelBuilder modelBuilder,
        //    Expression<Func<TBase, bool>> filter) where TBase : class
        //{
        //    // Iterates through all entity types in the model
        //    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        //    {
        //        // Checks if the entity type is a subclass of TBase
        //        if (entityType.ClrType.IsSubclassOf(typeof(TBase)))
        //        {
        //            var newParam = Expression.Parameter(entityType.ClrType);
        //            var newbody = ReplacingExpressionVisitor.
        //                Replace(filter.Parameters.Single(), newParam, filter.Body);

        //            // Applies the modified filter as a query filter to the entity type
        //            modelBuilder.Entity(entityType.ClrType).
        //                HasQueryFilter(Expression.Lambda(newbody, newParam));
        //        }
        //    }
        //}

        /// <summary>
        /// Allows multiple filters to be applied dynamically by accepting a list of filters
        /// </summary>
        /// <typeparam name="TBase"></typeparam>
        /// <param name="modelBuilder"></param>
        /// <param name="filters"></param>
        public static void ApplyGlobalFilters<TBase>(
            this ModelBuilder modelBuilder,
            params Expression<Func<TBase, bool>>[] filters) where TBase : class
        {
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (entityType.ClrType.IsSubclassOf(typeof(TBase)))
                {
                    var newParam = Expression.Parameter(entityType.ClrType);
                    Expression combinedBody = filters
                        .Select(filter => ReplacingExpressionVisitor.Replace(filter.Parameters.Single(), newParam, filter.Body))
                        .Aggregate((current, next) => Expression.AndAlso(current, next));

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(Expression.Lambda(combinedBody, newParam));
                }
            }
        }

        /// <summary>
        /// Applies filter based on a condition
        /// </summary>
        /// <typeparam name="TBase"></typeparam>
        /// <param name="modelBuilder"></param>
        /// <param name="filter"></param>
        /// <param name="condition"></param>
        public static void ApplyGlobalFilters<TBase>(
            this ModelBuilder modelBuilder,
            Expression<Func<TBase, bool>> filter, Func<Type, bool> condition) where TBase : class
        {
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (entityType.ClrType.IsSubclassOf(typeof(TBase)) && condition(entityType.ClrType))
                {
                    var newParam = Expression.Parameter(entityType.ClrType);
                    var newBody = ReplacingExpressionVisitor.Replace(filter.Parameters.Single(), newParam, filter.Body);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(Expression.Lambda(newBody, newParam));
                }
            }
        }

        
        /// <summary>
        /// Replace applied filter with new
        /// </summary>
        /// <typeparam name="TBase"></typeparam>
        /// <param name="modelBuilder"></param>
        /// <param name="filter"></param>
        //public static void ApplyGlobalFilters<TBase>(
        //    this ModelBuilder modelBuilder,
        //    Expression<Func<TBase, bool>> filter) where TBase : class
        //{
        //    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        //    {
        //        if (entityType.ClrType.IsSubclassOf(typeof(TBase)))
        //        {
        //            var newParam = Expression.Parameter(entityType.ClrType);
        //            var newBody = ReplacingExpressionVisitor.Replace(filter.Parameters.Single(), newParam, filter.Body);

        //            var entityTypeBuilder = modelBuilder.Entity(entityType.ClrType);
        //            var existingFilter = entityType.GetQueryFilter();

        //            if (existingFilter is not null)
        //            {
        //                var combinedBody = Expression.AndAlso(existingFilter.Body, newBody);
        //                entityTypeBuilder.HasQueryFilter(Expression.Lambda(combinedBody, newParam));
        //            }
        //            else
        //            {
        //                entityTypeBuilder.HasQueryFilter(Expression.Lambda(newBody, newParam));
        //            }
        //        }
        //    }
        //}
    }
}
