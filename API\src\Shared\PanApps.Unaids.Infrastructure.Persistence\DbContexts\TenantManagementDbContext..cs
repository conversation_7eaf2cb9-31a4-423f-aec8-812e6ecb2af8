﻿using Microsoft.EntityFrameworkCore;
using PanApps.Model.Administration;
using PanApps.Unaids.Domain;
using PanApps.Unaids.Domain.System;
using PanApps.Unaids.Domain.TenantManagement;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class TenantManagementDbContext(DbContextOptions<TenantManagementDbContext> options) : DbContext(options)
    {
        public DbSet<Program> Programs { get; set; }
        public DbSet<BusinessUnit> BusinessUnits { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<ProgramStage> ProgramStages { get; set; }
        public DbSet<LookUpType> LookUpTypes { get; set; }
        public DbSet<LookUpInfo> LookUpInfos { get; set; }
        public DbSet<Menu> Menus { get; set; }
        public DbSet<Module> Modules { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<RoleMenuMaster> RoleMenuMasters { get; set; }
        public DbSet<RoleMenuMapping> RoleMenuMappings { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.RegisterModels();
            // add shadow properties
            builder.AddShadowProperties();
            // Applies the query filter globally to all entities derived from BaseEntity
            builder.ApplyGlobalFilters<BaseEntity>(e => !e.IsDeleted);

            // Make referential delete behaviour restrict instead of cascade for everything
            foreach (var relationship in builder.Model.GetEntityTypes()
                         .SelectMany(x => x.GetForeignKeys()))
            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;
            }

            base.OnModelCreating(builder);
        }

    }
}
