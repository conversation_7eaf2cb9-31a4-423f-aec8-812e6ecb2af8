﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.9.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PanApps.PanGMS.Infrastructure.Persistence\PanApps.PanGMS.Infrastructure.Persistence.csproj" />
  </ItemGroup>

</Project>
