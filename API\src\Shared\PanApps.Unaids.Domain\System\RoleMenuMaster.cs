using PanApps.Unaids.Domain;
using PanApps.Unaids.Domain.System;

namespace PanApps.Model.Administration
{
    public class RoleMenuMaster : BaseEntity
    {
        public int BusinessUnitId { get; set; }
        public int RoleId { get; set; }
        public virtual Role? Role { get; set; }
        public int ModuleId { get; set; }
        public virtual Module? Module { get; set; }
        public short? IsSystemDefined { get; set; }
        public bool EqualRoleLevelFlag { get; set; }
        public ICollection<RoleMenuMapping>? RoleMenuMapping { get; set; }
    }
}
