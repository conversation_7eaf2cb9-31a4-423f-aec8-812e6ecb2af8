﻿using System.Text.Json;

namespace PanApps.Unaids.Domain
{
    public class TriggerRules
    {
        public string Action { get; set; } = string.Empty;
        public string Table { get; set; } = string.Empty;
        public List<Condition> Conditions { get; set; } = new();
    }

    public class Condition
    {
        public string Table { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public JsonDocument Value { get; set; } = default!;
    }

    public class WorkflowStepProperties
    {
        public Rules Rules { get; set; } = new Rules();
        public List<Activity> Activities { get; set; } = new();
        public List<Permission> Permissions { get; set; } = new();
    }

    public class Rules
    {
        public string Action { get; set; } = string.Empty;
        public string Table { get; set; } = string.Empty;
        public List<Condition> Conditions { get; set; } = new();
    }

    public class Activity
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }

    public class Permission
    {
        public string ActivityName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }
}
