﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class WorkflowPermissionConfiguration : IEntityTypeConfiguration<WorkflowPermission>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowPermission> builder)
        {
            builder.ToTable("workflow_permissions");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.WorkflowDefinitionId).IsRequired();
            builder
                .HasOne(e => e.WorkflowDefinition)
                .WithMany()
                .HasForeignKey(e => e.WorkflowDefinitionId)
                .HasPrincipalKey(e => e.Id);

            // builder.Property(e => e.RoleId).IsRequired();
            // builder
            //     .HasOne(e => e.Role)
            //     .WithMany()
            //     .HasForeignKey(e => e.RoleId)
            //     .HasPrincipalKey(e => e.Id);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowPermissionConfiguration());
        }
    }
}
