﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class WorkflowTransitionConfiguration : IEntityTypeConfiguration<WorkflowTransition>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowTransition> builder)
        {
            builder.ToTable("workflow_transitions");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.FromStepId).IsRequired();
            builder.Property(e => e.ToStepId).IsRequired();
            builder.Property(e => e.Property).HasColumnType("jsonb");
            
            //builder.OwnsOne(e => e.Property, ownedNavigationBuilder =>
            //{
            //    ownedNavigationBuilder.ToJson();
            //    ownedNavigationBuilder.OwnsMany(p => p.Permissions);
            //});
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowTransitionConfiguration());
        }
    }
}
