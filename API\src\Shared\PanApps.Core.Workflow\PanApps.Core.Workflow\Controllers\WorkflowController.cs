﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PanApps.Core.Base.Contracts;

namespace PanApps.Core.Workflow.Controllers
{
    [ApiController]
    [Route("api/workflows")]
    public class WorkflowController(IWorkflowRegistry workflowRegistry, IUserContext userContext): ControllerBase
    {
        /// <summary>
        /// Workflow change action
        /// </summary>
        /// <param name="instanceId"></param>
        /// <param name="stepId"></param>
        /// <param name="workflowTransition"></param>
        /// <returns></returns>
        [HttpPost("{instanceId}/transitions/{stepId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateWorkflow(Guid instanceId, int stepId, [FromBody] WorkflowTransitionDTO workflowTransition)
        {
            var variables = new Variables();
            variables.Set("comment", workflowTransition.Comments ?? "");

            // Trigger workflow
            var executionResult = await workflowRegistry.TriggerWorkflow(instanceId, stepId, userContext.Id, variables); 

            return Ok(executionResult);
        }

        /// <summary>
        /// Workflow transition - return action
        /// </summary>
        /// <param name="instanceId"></param>
        /// <param name="workflowTransition"></param>
        /// <returns></returns>
        [HttpPost("{instanceId}/returned-transitions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> Return(Guid instanceId, [FromBody] WorkflowTransitionDTO workflowTransition)
        {
            var variables = new Variables();
            variables.Set("comment", workflowTransition.Comments ?? "");

            // Trigger workflow
            var result = await workflowRegistry.Return(instanceId, userContext.Id, variables); 

            return Ok(result);
        }

        /// <summary>
        /// workflow transition - re-submit action
        /// </summary>
        /// <param name="instanceId"></param>
        /// <param name="workflowTransition"></param>
        /// <returns></returns>
        [HttpPost("{instanceId}/resubmitted-transtions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> ReSubmit(Guid instanceId, [FromBody] WorkflowTransitionDTO workflowTransition)
        {
            var variables = new Variables();
            variables.Set("comment", workflowTransition.Comments ?? "");

            // Trigger workflow
            var result = await workflowRegistry.ReSubmit(instanceId, userContext.Id, variables); // TO DO: Change the user ID with user context

            return Ok(result);
        }



    }
}
