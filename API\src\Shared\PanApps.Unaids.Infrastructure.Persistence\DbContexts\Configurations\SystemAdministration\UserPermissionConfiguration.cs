using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class UserPermissionConfiguration : IEntityTypeConfiguration<UserPermission>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserPermission> builder)
        {
            builder.ToTable("user_permissions");

            builder.HasKey(e => e.Id);

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.UserId)
                   .IsRequired();

            builder.Property(e => e.BusinessUnitId)
                   .IsRequired();

            builder.Property(e => e.AppId)
                   .IsRequired(false);

            builder.Property(e => e.RoleId)
                   .IsRequired();

            builder.HasOne(e => e.Role)
                   .WithMany()
                   .HasForeignKey(e => e.RoleId);

            builder.Property(e => e.ModuleGroupId)
                   .IsRequired(false);

            builder.Property(e => e.ModuleId)
                   .IsRequired();

            builder.Property(e => e.EffectiveFrom)
                   .IsRequired();

            builder.Property(e => e.EffectiveTo)
                   .IsRequired(false);

            builder.Property(e => e.IsSystemDefined)
                   .IsRequired(false);

            builder.Property(e => e.BusinessUnitValue)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(e => e.UserSameLevelFlag)
                   .IsRequired();

            builder.Property(e => e.PermissionFlag)
                   .IsRequired();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new UserPermissionConfiguration());
        }
    }
}
