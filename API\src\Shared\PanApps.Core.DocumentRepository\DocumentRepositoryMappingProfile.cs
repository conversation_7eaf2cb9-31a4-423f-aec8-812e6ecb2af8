﻿using AutoMapper;
using PanApps.PanGMS.Domain;

namespace PanApps.Core.DocumentRepository
{
    public class DocumentRepositoryMappingProfile: Profile
    {
        public DocumentRepositoryMappingProfile() 
        {
            CreateMap<DocumentStoreDetailDto, DocumentStoreDetails>().ReverseMap();
            CreateMap<DocumentRequestDto, DocumentStoreDetails>().ReverseMap();
            CreateMap<DocumentRequestDto, DocumentStore>().ReverseMap();
        }
    }
}
