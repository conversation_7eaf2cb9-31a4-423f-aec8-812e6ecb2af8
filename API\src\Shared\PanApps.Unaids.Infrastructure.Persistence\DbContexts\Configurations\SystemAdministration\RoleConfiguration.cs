using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class RoleConfiguration : IEntityTypeConfiguration<Role>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.ToTable("role_info");
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever();
            builder.Property(c => c.Code).IsRequired().HasMaxLength(10);
            builder.Property(c => c.Name).IsRequired().HasMaxLength(100);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RoleConfiguration());
        }
    }
}
