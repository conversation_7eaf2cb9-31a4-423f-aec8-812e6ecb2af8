﻿namespace PanApps.Core.DataAccess.Dynamic
{
    public class DbEntity
    {
        public string Schema { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<EntityProperties> Properties { get; set; } = new List<EntityProperties>();
    }

    public class EntityProperties
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }
}
