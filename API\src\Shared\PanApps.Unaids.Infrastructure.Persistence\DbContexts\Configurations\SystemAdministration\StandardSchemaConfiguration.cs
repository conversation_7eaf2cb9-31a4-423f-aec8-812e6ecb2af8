﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class StandardSchemaConfiguration : IEntityTypeConfiguration<StandardSchema>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<StandardSchema> builder)
        {
            builder.ToTable("standardschemas");
            builder.Has<PERSON>ey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever();
            builder.HasOne(c => c.Menu).WithMany().HasForeignKey(x => x.MenuId);
            builder.Navigation(c => c.Menu).AutoInclude();
            builder.Property(c => c.Schema).IsRequired(false).HasColumnType("json");
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new StandardSchemaConfiguration());
        }
    }
}
