﻿using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;
using PanApps.Core.Workflow.Models;
using PanApps.Unaids.Infrastructure.Persistence;
using System.Text.Json;

namespace PanApps.Core.Workflow
{
    public class WorkflowUtils(IUnitOfWork<SystemAdminDbContext> unitOfWork, IUserContext userContext): IWorkflowUtils
    {
        /// <summary>
        /// Build workflow steps 
        /// </summary>
        /// <param name="workflowDefinitionId"></param>
        /// <param name="userId"></param>
        /// <param name="manual"></param>
        /// <returns></returns>
        public async Task<WorkflowPermissionMapping> BuildWorkflowSteps(int workflowDefinitionId, int userId, bool manual = true)
        {
            var workflow = new WorkflowPermissionMapping();

            workflow.WorkflowDefinitionId = workflowDefinitionId;

            var roles = userContext.Roles;

            var repo = unitOfWork.Repository<IWorkflowRepository>();

            var hierarchies = await repo.GetWorkflowPermissions(workflowDefinitionId, roles);

            List<WorkflowStepHierarchy> userSteps = new();

            foreach (var item in hierarchies)
            {
                var hierarchy = item.Deserialize<List<WorkflowStepHierarchy>>(); 

                if (hierarchy != null)
                    userSteps.AddRange(hierarchy);
            }

            var steps = await repo.GetWorkflowSteps(workflowDefinitionId);

            workflow.Transitions = (from step in userSteps
                                    where (step.Manual == true || (manual == false && step.Auto == true))
                                   group step by new { step.FromStepId, step.ToStepId } into grp
                                    select new WorkflowTransition
                                    {
                                        FromStepId = grp.Key.FromStepId ?? 0,
                                        ToStepId = grp.Key.ToStepId ?? 0,
                                        TriggerService = (grp.Where(x => (x.TriggerService ?? false) == true).Count() > 0 || (steps.Where(x => x.Id == grp.Key.ToStepId).Select(x => x.TriggerService).FirstOrDefault() == 1)),
                                    }).ToList();

            workflow.StepHierarchy = (from step in userSteps
                                      where (step.Manual == true || (manual == false && step.Auto == true))
                                      group step by step.FromStepId into grp
                                      select new
                                      {
                                          ID = grp.Key ?? 0,
                                          ToSteps = grp.Select(x => x.ToStepId ?? 0).Distinct().ToArray()
                                      }).ToDictionary(o => o.ID, o => o.ToSteps);

            return workflow;
        }

        #region Get workflow history with next action item
        /// <summary>
        /// Get workflow history with next action item
        /// </summary>
        /// <param name="instanceId"></param>
        public async Task<WorkflowJourney> GetHistoryWithActionItem(Guid instanceId, int userId, string language)
        {
            var journey = new WorkflowJourney();

            var repo = unitOfWork.Repository<IWorkflowRepository>();

            var instance = await repo.GetWorkflowInstance(instanceId);

            // Get parent workflow steps
            var parentSteps = await repo.GetWorkflowSteps(instance.WorkflowDefinitionId);

            journey.CurrentStep = await repo.GetCurrentStep(instanceId);

            int[] roles = [1]; // TO DO: get roles from context
            var hierarchies = await repo.GetWorkflowPermissions(instance.WorkflowDefinitionId, roles);

            List<WorkflowStepHierarchy> userSteps = new();

            foreach (var item in hierarchies)
            {
                var hierarchy = item.Deserialize<List<WorkflowStepHierarchy>>(); // JsonConvert.DeserializeObject<List<WorkflowStepHierarchyDto>>(item);

                if (hierarchy != null)
                    userSteps.AddRange(hierarchy);
            }

            List<int> nextSteps = new ();
            if (journey.CurrentStep != null)
            {
                nextSteps = userSteps
                .Where(x => x.FromStepId == journey.CurrentStep.WorkFlowStepId && x.Manual == true)
                .Select(x => x.ToStepId ?? 0).Distinct().ToList();
            }

            journey.ActionItems = parentSteps
                .Where(x => nextSteps.Contains(x.Id))
                .Select(x => new WorkflowAction
                {
                    StepId = x.Id,
                    Action = x.Action,
                    Status = x.StepName
                }).ToList();

            journey.Journey = parentSteps.Select(x => new WorkflowJourneyStep() { 
                StepId  = x.Id,
                StepNo = x.StepNo,
                Action = x.Action,
                AllowRollback = x.AllowRollback,
                CanPause = x.CanPause,
                Status = x.DisplayName,
                ReturnStatus = x.ReturnStatus
            }).ToList();

            // Get workflow history
            journey.LogStatus = await repo.GetWorklowHistory(instanceId);

            return journey;
        }

        #endregion
    }
}
