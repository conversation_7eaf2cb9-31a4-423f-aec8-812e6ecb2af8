﻿using PanApps.Core.Base.DTOs;

namespace PanApps.Core.Base
{
    public interface ITaxonomyRepository
    {
        Task<List<ModelDTO>> GetModels();
        Task<List<ConceptDTO>> GetConcepts();
        Task<List<DataElementDTO>> GetConceptElements(string slug);
        Task<List<DataElementDTO>> GetModelElements(string model);
        Task<List<DataElementDTO>> GetDataElements();
        Task<List<ModelBuilderElementsDTO>> GetModelElements(int modelId);
    }
}
