using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class SettingConfiguration : IEntityTypeConfiguration<Setting>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Setting> builder)
        {
            builder.ToTable("settings");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Code).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Criteria).IsRequired().HasMaxLength(200);
            builder.Property(e => e.Value).IsRequired().HasMaxLength(500);
            builder.Property(e => e.Group).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Remarks).IsRequired(false).HasMaxLength(500);
            builder.Property(e => e.BusinessRules).IsRequired(false).HasMaxLength(500);

            builder.Property(e => e.ModuleId).IsRequired();
            builder
                .HasOne(e => e.Module)
                .WithMany()
                .HasForeignKey(e => e.ModuleId)
                .HasPrincipalKey(e => e.Id);

            builder.Property(e => e.DataTypeId).IsRequired();
            builder
                .HasOne(e => e.DataType)
                .WithMany()
                .HasForeignKey(e => e.DataTypeId)
                .HasPrincipalKey(e => e.Id);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new SettingConfiguration());
        }
    }
}
