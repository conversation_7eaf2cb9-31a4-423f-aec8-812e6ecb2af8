﻿namespace PanApps.Unaids.Domain.System
{
    public class Module: BaseEntity
    {
        public string? Code { get; set; }
        public string Name { get; set; } = "";
        public string? Description { get; set; }
        public int ModuleGroupId { get; set; }
        public virtual ModuleGroup? ModuleGroup { get; set; }
        public int? SortOrder { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? DashboardURL { get; set; }
    }
}
