﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class SequenceConfiguration: IEntityTypeConfiguration<Sequence>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Sequence> builder)
        {
            builder.ToTable("sequence_info");
            builder.<PERSON><PERSON><PERSON>(vf => vf.Id);
            builder.HasIndex(c => c.TableName).IsUnique();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new SequenceConfiguration());
        }
    }
}
