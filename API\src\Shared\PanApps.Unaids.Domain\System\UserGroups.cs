using PanApps.Unaids.Domain;
using System.ComponentModel.DataAnnotations.Schema;

namespace PanApps.Model.Administration
{
    [Table(name: "UserGroups")]
    public class UserGroups : BaseEntity  
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;

        // Navigation property for related UserGroupMapping entities
        public ICollection<UserGroupMapping>? UserGroupMapping { get; set; }
    }
}
