﻿
using AutoMapper;
using PanApps.Core.Base.Contracts;
using PanApps.Core.Base;
using PanApps.Unaids.Infrastructure.Persistence;

namespace PanApps.Authentication.Shared
{
    public class AzureADUserManager (IUnitOfWork<CommonDbContext> _unitOfWork,
        IMapper _mapper,
        ISequenceRepository _sequenceRepository,
        ITenantProvider _tenantProvider,
        IUserContext _userContext) : BaseUserManager(
            _unitOfWork,
            _mapper,
            _sequenceRepository,
            _tenantProvider,
            _userContext), IUserManager
    {

    }
}
