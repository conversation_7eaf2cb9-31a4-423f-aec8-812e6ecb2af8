﻿namespace PanApps.Unaids.Domain.System
{
    public class User : BaseEntity
    {
        public required string UserName { get; set; }
        public required string LoginName { get; set; }
        public int? RoleId { get; set; }
        public virtual Role? Role { get; set; }
        public required string NormalizedLoginName { get; set; }
        public required string Email { get; set; }
        public required string NormalizedEmail { get; set; }
        public required string PasswordHash { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public required bool IsActive { get; set; }
        public required bool IsLocked { get; set; }
        public int? StatusId { get; set; }

        public ICollection<PasswordHistory>? PasswordHistories { get; set; }
    }
}
