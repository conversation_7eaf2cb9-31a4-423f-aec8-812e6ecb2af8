﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;

namespace PanApps.Authorization.Shared
{
    public class AuthorizationPolicyProvider: DefaultAuthorizationPolicyProvider
    {
        public AuthorizationPolicyProvider(IOptions<AuthorizationOptions> options) : base(options)
        {

        }

        public override async Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
        {
            if (string.IsNullOrWhiteSpace(policyName))
            {
                return await base.GetPolicyAsync(policyName);
            }

            var permissions = policyName.Split(',');

            return new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .AddRequirements(new PermissionAuthorizationRequirement(permissions))
                .Build();
        }
    }
}
