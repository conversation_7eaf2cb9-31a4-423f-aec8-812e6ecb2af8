using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Unaids.Domain.System;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class RolePermissionMappingConfiguration : IEntityTypeConfiguration<RolePermissionMapping>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<RolePermissionMapping> builder)
        {
            builder.ToTable("role_permission_mappings");

            builder.HasKey(e => e.Id);

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.RoleId).IsRequired();

            builder.HasOne(e => e.Role)
                   .WithMany()
                   .HasForeignKey(e => e.RoleId)
                   .OnDelete(DeleteBehavior.Restrict);

            builder.Property(e => e.MenuAdd).IsRequired();
            builder.Property(e => e.MenuModify).IsRequired();
            builder.Property(e => e.MenuCancel).IsRequired();
            builder.Property(e => e.MenuView).IsRequired();
            builder.Property(e => e.MenuPrint).IsRequired();
            builder.Property(e => e.MenuRePrint).IsRequired();
            builder.Property(e => e.MenuDelete).IsRequired();
            builder.Property(e => e.MenuProcess).IsRequired();
            builder.Property(e => e.MenuApprove).IsRequired();
            builder.Property(e => e.MenuPreDatedEntry).IsRequired();
            builder.Property(e => e.MenuImport).IsRequired();
            builder.Property(e => e.MenuExport).IsRequired();
            builder.Property(e => e.MenuValidation).IsRequired();
            builder.Property(e => e.MenuCorrect).IsRequired();
            builder.Property(e => e.MenuBulkImport).IsRequired();
            builder.Property(e => e.MenuBulkUpdate).IsRequired();
            builder.Property(e => e.MenuBulkDelete).IsRequired();
            builder.Property(e => e.MenuExportRecord).IsRequired();
            builder.Property(e => e.RestrictedView).IsRequired();
            builder.Property(e => e.MenuJSONEdit).IsRequired();
            builder.Property(e => e.MenuSpecial1).IsRequired();
            builder.Property(e => e.MenuSpecial2).IsRequired();
            builder.Property(e => e.MenuSpecial3).IsRequired();
            builder.Property(e => e.MenuSpecial4).IsRequired();
            builder.Property(e => e.MenuSpecial5).IsRequired();

            builder.Property(e => e.IsSystemDefined).IsRequired(false);
            builder.Property(e => e.Active).IsRequired(false);
            builder.Property(e => e.ModelDefinitionId).IsRequired(false);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RolePermissionMappingConfiguration());
        }
    }
}
