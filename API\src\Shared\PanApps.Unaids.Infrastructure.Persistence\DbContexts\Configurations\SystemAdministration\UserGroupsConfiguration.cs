using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Model.Administration;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class UserGroupsConfiguration : IEntityTypeConfiguration<UserGroups>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserGroups> builder)
        {
            builder.ToTable("user_groups");

            builder.HasKey(e => e.Id);

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.Code)
                   .IsRequired()
                   .HasMaxLength(50);

            builder.Property(e => e.Name)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(e => e.Email)
                   .IsRequired()
                   .HasMaxLength(200);

            builder.HasMany(e => e.UserGroupMapping)
                   .WithOne(e => e.UserGroups)
                   .HasForeignKey(e => e.UserGroupsId)
                   .OnDelete(DeleteBehavior.Restrict);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new UserGroupsConfiguration());
        }
    }
}
