﻿using Microsoft.EntityFrameworkCore;
using PanApps.Core.Base;
using PanApps.Core.Base.DTOs;
using PanApps.PanGMS.Infrastructure.Persistence;
using System.Linq;

namespace PanApps.Core.Taxonomy.Repositories
{
    public class TaxonomyRepository(ProgramAdminDbContext dbContext, IGenericDropdownRepository genericRepository) : ITaxonomyRepository
    {
        public async Task<List<ModelDTO>> GetModels()
        {
            return await (from model in dbContext.Models.AsNoTracking()
                          join pModel in dbContext.ProgramModels.AsNoTracking()
                          on model.Id equals pModel.ModelId
                          select new ModelDTO()
                          {
                              Id = model.Id,
                              Name = model.Name,
                              DisplayName = pModel.DisplayName,
                              Slug = model.Slug
                          }).ToListAsync();

        }
        public async Task<List<ConceptDTO>> GetConcepts()
        {
            return await (from concept in dbContext.Concepts.AsNoTracking()
                          join pConcept in dbContext.ProgramConcepts.AsNoTracking()
                          on concept.Id equals pConcept.ConceptId
                          select new ConceptDTO()
                          {
                              Id = concept.Id,
                              Name = concept.Name,
                              Slug = concept.Slug,
                              Description = concept.Description,
                              SubjectToChange = concept.SubjectToChange,
                              Synonym = concept.Synonym
                          }).ToListAsync();

        }



        public async Task<List<DataElementDTO>> GetConceptElements(string slug)
        {
            var globalElements = await (from dElement in dbContext.DataElements.AsNoTracking()
                          join concept in dbContext.Concepts.AsNoTracking()
                          on dElement.ConceptId equals concept.Id
                          join lkp in dbContext.LookUpInfo.AsNoTracking()
                          on dElement.DataTypeId equals lkp.Id into lk
                          from lookup in lk.DefaultIfEmpty()
                          where concept.Slug == slug
                          select new DataElementDTO()
                          {
                              Id = dElement.Id,
                              Name = dElement.Name,
                              Slug = dElement.Slug,
                              Code = dElement.Code,
                              DataGroupName = dElement.DataGroupName,
                              DataType = (lookup != null) ? lookup.Name : null,
                              Description = dElement.Description,
                              Format = dElement.Format,
                              MaxLength = dElement.MaxLength
                          }).ToListAsync();

            var localElements = await (from dElement in dbContext.LocalDataElements.AsNoTracking()
                                       join concept in dbContext.Concepts.AsNoTracking()
                                       on dElement.ConceptId equals concept.Id
                                       join lkp in dbContext.LookUpInfo.AsNoTracking()
                                       on dElement.DataTypeId equals lkp.Id into lk
                                       from lookup in lk.DefaultIfEmpty()
                                       where concept.Slug == slug
                                       select new DataElementDTO()
                                       {
                                           Id = dElement.Id,
                                           Name = dElement.Name,
                                           Slug = dElement.Slug,
                                           Code = dElement.Code,
                                           DataGroupName = dElement.DataGroupName,
                                           DataType = (lookup != null) ? lookup.Name : null,
                                           Description = dElement.Description,
                                           Format = dElement.Format,
                                           MaxLength = dElement.MaxLength
                                       }).ToListAsync();

            return localElements.Union(globalElements).ToList();

        }

        public async Task<List<ModelBuilderElementsDTO>> GetModelElements(int modelId)
        {
            var dataelements = await (from dElement in dbContext.DataElements.AsNoTracking()
                                      join mDataElement in dbContext.ModelDataElements.AsNoTracking()
                                      on dElement.Id equals mDataElement.DataElementId
                                      join pModel in dbContext.ProgramModels.AsNoTracking()
                                      on mDataElement.ModelId equals pModel.ModelId
                                      join lkp in dbContext.LookUpInfo.AsNoTracking()
                                      on dElement.DataTypeId equals lkp.Id into lk
                                      from lookup in lk.DefaultIfEmpty()
                                      where pModel.Id == modelId && mDataElement.IsGlobal == 1
                                      select new
                                      {
                                          dElement.Id,
                                          dElement.Name,
                                          dElement.Slug,
                                          dElement.Code,
                                          DataType = (lookup != null) ? lookup.Name : null,
                                          dElement.Description,
                                          dElement.Format,
                                          dElement.MaxLength,
                                          mDataElement.Options
                                      }).ToListAsync();

            var localElements = await (from dElement in dbContext.LocalDataElements.AsNoTracking()
                                       join mDataElement in dbContext.ModelDataElements.AsNoTracking()
                                        on dElement.Id equals mDataElement.DataElementId
                                       join pModel in dbContext.ProgramModels.AsNoTracking()
                                     on mDataElement.ModelId equals pModel.ModelId
                                       join lkp in dbContext.LookUpInfo.AsNoTracking()
                                        on dElement.DataTypeId equals lkp.Id into lk
                                       from lookup in lk.DefaultIfEmpty()
                                       where pModel.Id == modelId && mDataElement.IsGlobal != 1
                                       select new
                                       {
                                           dElement.Id,
                                           dElement.Name,
                                           dElement.Slug,
                                           dElement.Code,
                                           DataType = (lookup != null) ? lookup.Name : null,
                                           dElement.Description,
                                           dElement.Format,
                                           dElement.MaxLength,
                                           mDataElement.Options
                                       }).ToListAsync();

            return localElements.Union(dataelements).Select(x => new ModelBuilderElementsDTO()
            {
                Code = x.Code,
                Slug = x.Slug,
                DataType = x.DataType,
                MaxLength = x.MaxLength,
                Name = x.Name,
                OptionsFromURL = x.Options
            }).ToList();
        }

        public async Task<List<DataElementDTO>> GetModelElements(string model)
        {
            var dataelements = await (from dElement in dbContext.DataElements.AsNoTracking()
                                      join mDataElement in dbContext.ModelDataElements.AsNoTracking()
                                      on dElement.Id equals mDataElement.DataElementId
                                      join m in dbContext.Models.AsNoTracking()
                                      on mDataElement.ModelId equals m.Id
                                      join lkp in dbContext.LookUpInfo.AsNoTracking()
                                      on dElement.DataTypeId equals lkp.Id into lk
                                      from lookup in lk.DefaultIfEmpty()
                                      where (m.Name == model || m.Slug == model) && mDataElement.IsGlobal == 1
                                      select new
                                      {
                                          dElement.Id,
                                          dElement.Name,
                                          dElement.Slug,
                                          dElement.Code,
                                          dElement.DataGroupName,
                                          DataType = (lookup != null) ? lookup.Name : null,
                                          dElement.Description,
                                          dElement.Format,
                                          dElement.MaxLength,
                                          mDataElement.NavigationModelId,
                                          mDataElement.Options
                                      }).ToListAsync();

            var localElements = await (from dElement in dbContext.LocalDataElements.AsNoTracking()
                                       join mDataElement in dbContext.ModelDataElements.AsNoTracking()
                                        on dElement.Id equals mDataElement.DataElementId
                                       join m in dbContext.Models.AsNoTracking()
                                       on mDataElement.ModelId equals m.Id
                                       join lkp in dbContext.LookUpInfo.AsNoTracking()
                                        on dElement.DataTypeId equals lkp.Id into lk
                                       from lookup in lk.DefaultIfEmpty()
                                       where (m.Name == model || m.Slug == model) && mDataElement.IsGlobal != 1
                                       select new 
                                       {
                                           dElement.Id,
                                           dElement.Name,
                                           dElement.Slug,
                                           dElement.Code,
                                           dElement.DataGroupName,
                                           DataType = (lookup != null) ? lookup.Name : null,
                                           dElement.Description,
                                           dElement.Format,
                                           dElement.MaxLength,
                                           mDataElement.NavigationModelId,
                                           mDataElement.Options
                                       }).ToListAsync();

            dataelements = dataelements.Union(localElements).ToList();

            var navigations = dataelements.Where(X => X.NavigationModelId != null).Select(x => x.NavigationModelId).Distinct().ToList();

            if (navigations.Any())
            {
                var navigationDataElements = await (from dElement in dbContext.DataElements.AsNoTracking()
                                                    join mDataElement in dbContext.ModelDataElements.AsNoTracking()
                                                    on dElement.Id equals mDataElement.DataElementId
                                                    join m in dbContext.Models.AsNoTracking()
                                                    on mDataElement.ModelId equals m.Id
                                                    join lkp in dbContext.LookUpInfo.AsNoTracking()
                                                    on dElement.DataTypeId equals lkp.Id into lk
                                                    from lookup in lk.DefaultIfEmpty()
                                                    where navigations.Contains(m.Id) && mDataElement.IsGlobal == 1
                                                    select new
                                                    {
                                                        ModelId = m.Id,
                                                        ModelName = m.Name,
                                                        dElement.Id,
                                                        dElement.Name,
                                                        dElement.Slug,
                                                        dElement.Code,
                                                        dElement.DataGroupName,
                                                        DataType = (lookup != null) ? lookup.Name : null,
                                                        dElement.Description,
                                                        dElement.Format,
                                                        dElement.MaxLength,
                                                        mDataElement.NavigationModelId,
                                                        mDataElement.Options
                                                    }).ToListAsync();

                var dropdowns = (navigationDataElements.Where(x => !string.IsNullOrEmpty(x.Options)).Select(x => x.Options).ToArray())
                    .Union(dataelements.Where(x => !string.IsNullOrEmpty(x.Options)).Select(x => x.Options).ToArray()).Distinct().ToArray();

                var dropDownOptions = await genericRepository.GetOptions(dropdowns, 0);


                return dataelements.Select(x => new DataElementDTO()
                {
                    Id = x.Id,
                    Name = x.Name,
                    Slug = x.Slug,
                    Code = x.Code,
                    DataGroupName = x.DataGroupName,
                    DataType = x.DataType,
                    Description = x.Description,
                    Format = x.Format,
                    MaxLength = x.MaxLength,
                    LookUps = dropDownOptions.ContainsKey(x.Options ?? "") ? dropDownOptions[x.Options ?? ""] : null,   
                    Navigation = navigationDataElements
                    .Where(y => y.ModelId == x.NavigationModelId)
                    .GroupBy(g => g.ModelId)
                    .Select(x => new ModelNavigation()
                    {
                        Id = x.Key,
                        Name = x.FirstOrDefault()?.ModelName,
                        DataElements = x.Select(y => new DataElementDTO()
                        {
                            Id = y.Id,
                            Name = y.Name,
                            Slug = y.Slug,
                            Code = y.Code,
                            DataGroupName = y.DataGroupName,
                            DataType = y.DataType,
                            Description = y.Description,
                            LookUps = dropDownOptions.ContainsKey(y.Options ?? "") ? dropDownOptions[y.Options ?? ""] : null,
                            Format = y.Format,
                            MaxLength = y.MaxLength,
                        }).ToList()
                    }).FirstOrDefault()
                }).ToList();

            }
            else
            {
                var dropdowns = dataelements.Where(x => !string.IsNullOrEmpty(x.Options)).Select(x => x.Options).Distinct().ToArray();

                var dropDownOptions = await genericRepository.GetOptions(dropdowns, 0);
                
                return dataelements.Select(x => new DataElementDTO()
                {
                    Id = x.Id,
                    Name = x.Name,
                    Slug = x.Slug,
                    Code = x.Code,
                    DataGroupName = x.DataGroupName,
                    DataType = x.DataType,
                    Description = x.Description,
                    Format = x.Format,
                    MaxLength = x.MaxLength,
                    LookUps = dropDownOptions.ContainsKey(x.Options ?? "") ? dropDownOptions[x.Options ?? ""] : null,
                }).ToList();
            }


        }
        public async Task<List<DataElementDTO>> GetDataElements()
        {
            var globalElements = await (from dElement in dbContext.DataElements.AsNoTracking()
                                        join pElement in dbContext.ProgramDataElements.AsNoTracking()
                                        on dElement.Id equals pElement.DataElementId
                                        join lkp in dbContext.LookUpInfo.AsNoTracking()
                                        on dElement.DataTypeId equals lkp.Id into lk
                                        from lookup in lk.DefaultIfEmpty()
                                        select new DataElementDTO()
                                        {
                                            Id = dElement.Id,
                                            Name = dElement.Name,
                                            Slug = dElement.Slug,
                                            Code = dElement.Code,
                                            DataGroupName = dElement.DataGroupName,
                                            DataType = (lookup != null) ? lookup.Name : null,
                                            Description = dElement.Description,
                                            Format = dElement.Format,
                                            MaxLength = dElement.MaxLength
                                        }).ToListAsync();

            var localElements = await (from dElement in dbContext.LocalDataElements.AsNoTracking()
                                       join lkp in dbContext.LookUpInfo.AsNoTracking()
                                       on dElement.DataTypeId equals lkp.Id into lk
                                       from lookup in lk.DefaultIfEmpty()
                                       select new DataElementDTO()
                                       {
                                           Id = dElement.Id,
                                           Name = dElement.Name,
                                           Slug = dElement.Slug,
                                           Code = dElement.Code,
                                           DataGroupName = dElement.DataGroupName,
                                           DataType = (lookup != null) ? lookup.Name : null,
                                           Description = dElement.Description,
                                           Format = dElement.Format,
                                           MaxLength = dElement.MaxLength
                                       }).ToListAsync();

            return globalElements.Union(localElements).ToList();
        }
    }
}
