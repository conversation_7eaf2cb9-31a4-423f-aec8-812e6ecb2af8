﻿using PanApps.Unaids.Domain.Common;

namespace PanApps.Unaids.Domain.System
{
    public class LookUpInfo: TaxonomyElement
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int LookUpTypeId { get; set; }
        public virtual LookUpType? LookUpType { get; set; }
        public decimal? SortOrder { get; set; }
        public short? IsSystemDefined { get; set; }

    }
}
