using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PanApps.Model.Administration;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class UserGroupMappingConfiguration : IEntityTypeConfiguration<UserGroupMapping>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserGroupMapping> builder)
        {
            builder.ToTable("user_group_mapping");

            builder.HasKey(e => e.Id);

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.UserGroupsId).IsRequired();

            builder.HasOne(e => e.UserGroups)
                   .WithMany()
                   .HasForeignKey(e => e.UserGroupsId)
                   .OnDelete(DeleteBehavior.Restrict);

            builder.Property(e => e.UserId).IsRequired();
            builder.Property(e => e.DefaultUserGroup).IsRequired();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new UserGroupMappingConfiguration());
        }
    }
}
