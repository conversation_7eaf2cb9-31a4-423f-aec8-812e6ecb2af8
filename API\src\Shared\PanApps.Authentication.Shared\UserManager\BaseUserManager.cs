﻿using AutoMapper;
using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;
using PanApps.Unaids.Domain.System;
using PanApps.Unaids.Infrastructure.Persistence;

namespace PanApps.Authentication.Shared
{
    public abstract class BaseUserManager (
        IUnitOfWork<CommonDbContext> _unitOfWork,
        IMapper _mapper,
        ISequenceRepository _sequenceRepository,
        ITenantProvider _tenantProvider,
        IUserContext _userContext)
    {
        /// <summary>
        /// Add the user
        /// </summary>
        /// <param name="userDto"></param>
        /// <returns></returns>
        public virtual async Task<UserDTO> AddUser(UserDTO userDto)
        {
            var user = _mapper.Map<User>(userDto);

            var repo = _unitOfWork.Repository<IUserRepository>();

            // Check whether user exists or not...
            if(await repo.ExistsAsync(user.Email))
            {
                // if yes, get the existing user...
                user = await repo.GetUserByEmail(user.Email);
            }
            else
            {
                // add new user..
                user.Id = await _sequenceRepository.GetSequenceNumber(repo.GetTableName());

                user.NormalizedEmail = user.Email.ToLower();
                user.NormalizedLoginName = user.LoginName.ToLower();
                user.PasswordHash = string.Empty;
                user.IsActive = true;
                user.IsLocked = false;

                user.CreatedAt = DateTime.UtcNow;
                user.CreatedById = _userContext.Id;

                await repo.AddAsync(user);

                // invite user...
                await Invite(user);
            }

            var userPermission = new UserPermission()
            {
                Id = await _sequenceRepository.GetSequenceNumber("UserPermissions"),
                BusinessUnitId = _tenantProvider.TenantId ?? 0,
                RoleId = user.RoleId ?? 0,
                UserId = user.Id,
                BusinessUnitValue = string.Empty,
                CreatedAt = DateTime.UtcNow,
                CreatedById = _userContext.Id                
            };

            await repo.AddUserPermission(userPermission);

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<UserDTO>(user);
        }

        /// <summary>
        /// Get the list of users of the tenant
        /// </summary>
        /// <returns></returns>
        public virtual async Task<List<UserDTO>> GetAll()
        {
            var users = await _unitOfWork.Repository<IUserRepository>().GetTenantUsers(_tenantProvider.TenantId ?? 0);

            return _mapper.Map<List<UserDTO>>(users);
        }

        /// <summary>
        /// Get the user by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual async Task<UserDTO> GetById(int id)
        {
            var user = await _unitOfWork.Repository<IUserRepository>().GetUser(id, _tenantProvider.TenantId ?? 0);

            return _mapper.Map<UserDTO>(user);
        }


        /// <summary>
        /// send the invitation link to the user
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public virtual async Task Invite(User user)
        {
            throw new NotImplementedException();
        }
    }
}
