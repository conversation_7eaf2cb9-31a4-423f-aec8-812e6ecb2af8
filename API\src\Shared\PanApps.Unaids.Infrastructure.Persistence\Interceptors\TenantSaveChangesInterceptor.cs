﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using PanApps.Core.Base;
using PanApps.Core.Base.Contracts;
using PanApps.Unaids.Domain;

namespace PanApps.Unaids.Infrastructure.Persistence
{
    public sealed class TenantSaveChangesInterceptor(ITenantProvider tenantProvider, IUserContext userContext): SaveChangesInterceptor
    {
        public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
        {
            if (eventData.Context != null)
                ApplyTenantId(eventData.Context);
            return base.SavingChanges(eventData, result);
        }
        public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
        {
            if (eventData.Context != null)
                ApplyTenantId(eventData.Context);
            return base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        private void ApplyTenantId(DbContext context)
        {
            if (context == null) return;
            int? tenantId = tenantProvider.TenantId;

            // Go through all new and modified entities tracked by the context
            foreach (var entry in context.ChangeTracker.Entries())
            {
                if (entry.State == EntityState.Added)
                {
                    // For new entities, set the TenantId shadow property
                    entry.Property("TenantId").CurrentValue = tenantId;
                }
                else if (entry.State == EntityState.Modified)
                {
                    string[] currProps= { "TenantId", "CreatedById", "CreatedAt" };
                    foreach (var prop in currProps)
                    {
                        var property = entry.Property(prop);

                        if (property.IsModified)
                        {
                            // If someone tried to change the TenantId (or it's somehow
                            property.CurrentValue = property.OriginalValue;
                        }
                    }

                    entry.Property("CreatedById").CurrentValue = userContext.Id;
                    entry.Property("CreatedAt").CurrentValue = DateTime.Now;
                }
            }

            foreach(var entry in context.ChangeTracker.Entries<BaseEntity>())
            {
                if (entry.State == EntityState.Added)
                {
                    // set the audit properties
                    entry.Property("IsDeleted").CurrentValue = false;
                    entry.Property("CreatedById").CurrentValue = userContext.Id;
                    entry.Property("CreatedAt").CurrentValue = DateTime.Now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    string[] currProps = { "CreatedById", "CreatedAt" };
                    foreach (var prop in currProps)
                    {
                        var property = entry.Property(prop);

                        if (property.IsModified)
                        {
                            // If someone tried to change the TenantId (or it's somehow
                            property.CurrentValue = property.OriginalValue;
                        }
                    }

                    entry.Property("UpdatedById").CurrentValue = userContext.Id;
                    entry.Property("UpdatedAt").CurrentValue = DateTime.Now;
                }
            }
        }
    }
}
