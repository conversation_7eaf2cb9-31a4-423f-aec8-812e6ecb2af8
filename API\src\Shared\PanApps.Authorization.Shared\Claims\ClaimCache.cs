﻿using Microsoft.Extensions.Caching.Memory;
using PanApps.Core.Base;
using System.Collections.Concurrent;

namespace PanApps.Authorization.Shared
{
    public class ClaimCache(IMemoryCache cache): IClaimCache
    {
        private static readonly ConcurrentDictionary<string, object> _locks = new();

        public UserClaims? GetToken(string cacheKey)
        {
            var cacheLock = _locks.GetOrAdd(cacheKey, new object());

            lock (cacheLock)
            {
                if (cache.TryGetValue(cacheKey, out UserClaims? claims))
                {
                    return claims;
                }
                else
                {
                    // Remove expired token if found
                    cache.Remove(cacheKey);

                    return null;
                }
            }
        }

        public void SetToken(string key, UserClaims claims)
        {
            var cacheLock = _locks.GetOrAdd(key, new object());

            lock (cacheLock)
            {
                // Set cache options with absolute expiration
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpiration = claims.ExpiryDate
                };

                cache.Set(key, claims, cacheOptions);
            }
        }

    }
}
